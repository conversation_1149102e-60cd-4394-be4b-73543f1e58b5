package com.zte.uedm.maintenance.manager.interfaces.web.vo;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class MaintenanceObjectVO {
    @ApiModelProperty("维保对象实例id")
    private String instanceId;

    @ApiModelProperty("维保对象实例名称")
    private String instanceName;

    @ApiModelProperty("维保对象类型")
    private String type;

    @ApiModelProperty("维保对象资源类型，区分监控设备监控对象")
    private String resourceType;

    @ApiModelProperty("维保对象选择的维保模板")
    private List<IdNameBean> maintenanceTemplate;
}
