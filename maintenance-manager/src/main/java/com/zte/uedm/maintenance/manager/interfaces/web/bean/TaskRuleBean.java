package com.zte.uedm.maintenance.manager.interfaces.web.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class TaskRuleBean {
    @ApiModelProperty("{\"zh_CN\":\"计划用时\",\"en_US\":\"Consume Time\"}")
    private Integer consumeTime;

    @ApiModelProperty("{\"zh_CN\":\"频率\",\"en_US\":\"Frequency\"}")
    private Integer frequency;

    @ApiModelProperty("{\"zh_CN\":\"粒度\",\"en_US\":\"Grain\"}")
    private String grain;

    @ApiModelProperty("{\"zh_CN\":\"任务时间\",\"en_US\":\"Task Time\"}")
    private List<TriggerBaseBean> taskDatetime;
}
