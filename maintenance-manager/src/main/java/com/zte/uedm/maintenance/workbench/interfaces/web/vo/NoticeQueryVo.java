package com.zte.uedm.maintenance.workbench.interfaces.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class NoticeQueryVo {

    @ApiModelProperty(value = "公告标题" ,required = true)
    private String vagueTitle;
    @ApiModelProperty(value = "开始时间" ,required = true)
    private String fromTime;
    @ApiModelProperty(value = "结束时间" ,required = true)
    private String endTime;
    @ApiModelProperty(value = "当前页数" ,required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "当页条数" ,required = true)
    private Integer pageSize;
}
