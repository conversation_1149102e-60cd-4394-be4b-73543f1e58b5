package com.zte.uedm.maintenance.order.infrastructure.repository.dutyorder.mapper;

import com.zte.uedm.maintenance.order.interfaces.web.vo.DutyOrderTime;
import com.zte.uedm.maintenance.order.interfaces.web.vo.DutyShiftVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29
 **/

@Mapper
public interface DutyOrderMapper {
    /**
     * 新增班次规则
     *
     * @param dutyOrderTimeList
     * @return
     */

    int addDutyOrderTimeRecord(@Param("dutyOrderTimeList") List<DutyOrderTime> dutyOrderTimeList);

    /**
     * 更新班次规则
     *
     * @param orderInfoId
     * @return
     */

    int updateDutyOrderTimeRecord(@Param("dutyOrderTimeList") List<DutyOrderTime> dutyOrderTimeList,
                                  @Param("orderInfoId") String orderInfoId);


    /**
     * 删除班次规则
     *
     * @param orderInfoId
     * @return
     */
    int deleteDutyOrderTimeRecord(@Param("orderInfoId") String orderInfoId);


    List<DutyOrderTime> showListById(@Param("orderInfoId") String orderInfoId);

    /**
     * 根据班次时间Id删除
     * @param deleteIdList
     * @return
     */

    int deleteDutyOrderTimeRecordByOrderTimeId(@Param("deleteIdList") List<String> deleteIdList);
}
