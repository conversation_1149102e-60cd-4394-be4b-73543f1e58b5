package com.zte.uedm.maintenance.manager.domain.shared.enums;

import java.util.Arrays;

public enum MaintenancePlanStatusEnums {
    NOT_ENABLED(1,"{\"zh_CN\":\"未启用\",\"en_US\":\"Not Enabled\"}"),
    ENABLED(2,"{\"zh_CN\":\"启用\",\"en_US\":\"Enabled\"}"),
    SUSPENDED(3,"{\"zh_CN\":\"暂停\",\"en_US\":\"Suspended\"}"),
    STOPPED(4,"{\"zh_CN\":\"终止\",\"en_US\":\"Stopped\"}");

    private final Integer status;
    private final String name;

    MaintenancePlanStatusEnums(Integer status,String name){
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName(){
        return name;
    }

    public static String getNameByStatus(Integer status){
        return Arrays
                .stream(MaintenancePlanStatusEnums.values())
                .filter(en -> en.getStatus().equals(status))
                .map(MaintenancePlanStatusEnums::getName)
                .findAny()
                .orElse("");
    }
}
