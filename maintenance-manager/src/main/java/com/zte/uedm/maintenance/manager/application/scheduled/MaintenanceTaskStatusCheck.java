package com.zte.uedm.maintenance.manager.application.scheduled;

import com.zte.uedm.maintenance.manager.application.executor.MaintenanceTaskCommandService;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenancetask.po.MaintenanceTaskPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@EnableScheduling
@Component
@Slf4j
public class MaintenanceTaskStatusCheck {

    @Resource
    private MaintenanceTaskCommandService maintenanceTaskCommandService;

    /**
     * 每一分钟扫描一次 更新 超期待执行 或者超期未执行 的任务 频率较高 debug日志
     */
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void taskStatusCheckWaiteExecute(){
        log.debug("begin taskStatusCheck");

        try{
            List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.checkExpiredTask();
            log.debug("taskStatusCheck finish,result={}",taskPOList);
        }catch (Exception e){
            log.error("taskGenerate error",e);
        }

        log.debug("taskStatusCheck finish");
    }
}
