package com.zte.uedm.maintenance.manager.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:xa2e9uddbekff5d14bec0ade80982b2c6cc0a22c */
@Getter
@Setter
@ToString
public class MocEntityVo {

    /**
     * ID
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属模型
     */
    private String model;
}
/* Ended by AICoder, pid:xa2e9uddbekff5d14bec0ade80982b2c6cc0a22c */
