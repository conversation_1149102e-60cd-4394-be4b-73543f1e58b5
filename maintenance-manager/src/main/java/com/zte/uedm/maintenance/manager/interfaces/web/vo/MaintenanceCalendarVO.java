package com.zte.uedm.maintenance.manager.interfaces.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class MaintenanceCalendarVO {
    @ApiModelProperty("维保日历对象ID")
    private String userId;

    @ApiModelProperty("维保日历对象详情")
    private List<String> detail;

    @ApiModelProperty("维保日历更新时间")
    private String updateTime;
}
