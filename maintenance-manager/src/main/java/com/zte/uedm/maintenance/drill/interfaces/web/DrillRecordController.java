package com.zte.uedm.maintenance.drill.interfaces.web;

import com.google.common.collect.Lists;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.drill.application.utils.DrillOperationLogUtils;
import com.zte.uedm.maintenance.drill.domain.service.DrillRecordService;
import com.zte.uedm.maintenance.drill.interfaces.web.bean.DrillRecordDetailBean;
import com.zte.uedm.maintenance.drill.interfaces.web.bean.ExportDrillRecordBean;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillRecordDetailDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillRecordEventDetailDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillRecordListDto;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.ResponseUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(value = "应急演练记录接口", tags = { "应急演练记录" })
@Controller
@Path("/emergency-drill/record")
public class DrillRecordController {

    private static final String SUCCESS = "success";

    @Autowired
    private DrillRecordService drillRecordService;

    @GET
    @Path("/detail/query")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "演练记录详情查询", notes = "演练记录详情查询", httpMethod = "GET")
    public ResponseBean getDrillRecordDetail(@QueryParam("recordId") String recordId,@HeaderParam("language-option") String languageOption) {
        DrillRecordDetailDto drillRecordDetailDto = null;
        try {
            drillRecordDetailDto = drillRecordService.getDrillRecordDetail(recordId,languageOption);
        }catch(UedmException e){
          return  ResponseUtils.getResponseBeanByUedmException(e);
        }
        return  ResponseUtils.getNormalResponseBean(0,SUCCESS, drillRecordDetailDto,1);
    }

    @POST
    @Path("/detail/query/condition")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "演练事件详情条件查询", notes = "演练事件详情条件查询", httpMethod = "POST")
    public ResponseBean getDrillRecordDetail(@RequestBody DrillRecordDetailBean drillRecordDetailBean, @HeaderParam("language-option") String languageOption) {
        List<DrillRecordEventDetailDto> res = drillRecordService.getDrillRecordDetailByCondition(drillRecordDetailBean, languageOption);
        int totalCount = drillRecordService.queryRecordEventDetailTotalCount(drillRecordDetailBean);
        return ResponseUtils.getNormalResponseBean(0,SUCCESS, res, totalCount);
    }

    @POST
    @Path("/detail/export")
    @Consumes({ MediaType.APPLICATION_JSON })
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "导出演练记录PDF", notes = "导出演练记录PDF", httpMethod = "POST")
    public ResponseBean exportDrillRecord(@RequestBody ExportDrillRecordBean exportDrillRecordBean, @HeaderParam("language-option") String languageOption, @Context HttpServletResponse response) {
        try {
            drillRecordService.exportDrillRecord(exportDrillRecordBean,languageOption,response);
        }catch(UedmException e){
           return ResponseUtils.getResponseBeanByUedmException(e);
        }
        return ResponseUtils.getNormalResponseBean(0, SUCCESS, SUCCESS, null);
    }



    @GET
    @Path("/query")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "演练记录查询", notes = "演练记录查询", httpMethod = "GET")
    public ResponseBean getDrillRecord(@QueryParam("templateName") String templateName,
                                       @QueryParam("order") String order,
                                       @QueryParam("sort") String sort,
                                       @QueryParam("pageNo") Integer pageNo,
                                       @QueryParam("pageSize") Integer pageSize,@HeaderParam("language-option") String languageOption) {
        List<DrillRecordListDto> pageList = null;
        List<DrillRecordListDto> drillRecordDetailDtos = drillRecordService.queryDrillRecord(templateName, order, sort, languageOption);
        pageList = PageUtil.getPageList(drillRecordDetailDtos, pageNo, pageSize);
        return ResponseUtils.getNormalResponseBean(0, SUCCESS, pageList, drillRecordDetailDtos.size());
    }

    @POST
    @Path("/start")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "开始演练", notes = "开始演练", httpMethod = "POST")
    public ResponseBean start(@QueryParam("templateId") String templateId, @HeaderParam("language-option") String languageOption) {
        try {
            drillRecordService.start(templateId, languageOption);
        } catch (Exception e) {
            log.error("start task error", e);
            return ResponseUtils.getResponseBeanByUedmException(e);
        }
        return ResponseUtils.getNormalResponseBean(0, SUCCESS, null);
    }

    @POST
    @Path("/interrupt")
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "中止演练", notes = "中止演练", httpMethod = "POST")
    public ResponseBean interrupt(@QueryParam("templateId") String templateId) {
        drillRecordService.cancel(templateId);
        return ResponseUtils.getNormalResponseBean(0, SUCCESS, null);
    }

    @GET
    @Path("/get-topology-snapshot-list")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取更新模板列表", notes = "获取更新模板列表", httpMethod = "GET")
    public ResponseBean getTopologySnapshotList(@QueryParam("recordId") String recordId,
                                                @HeaderParam("language-option") String language) {

        Map<String,Object> map = null;
        try {
            map = drillRecordService.getToplogySnapshot(recordId);
        } catch (UedmException e) {
            log.error("Get latest topo error", e);
            return ResponseUtils.getNormalResponseBean(-1, "Get template error", null);        }
        return ResponseUtils.getNormalResponseBean(0, Lists.newArrayList(map), null);

    }

    @GET
    @Path("/get-topology-snapshot")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取更新模板", notes = "获取更新模板", httpMethod = "GET")
    public ResponseBean getTopologySnapshot(@QueryParam("recordId") String recordId,
                                            @HeaderParam("language-option") String language) {

        Map<String,Object> map = null;
        try {
            map = drillRecordService.getToplogySnapshot(recordId);
        } catch (UedmException e) {
            log.error("Get latest topo error", e);
            return ResponseUtils.getNormalResponseBean(-1, "Get template error", null);        }
        return ResponseUtils.getNormalResponseBean(0, map, null);

    }
}
