package com.zte.uedm.maintenance.malfunction.infrastructure.client.license.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@ToString
public class LicenseResultBean
{
    /**
     * 运营商编号
     */
    private String operatorId;
    /**
     * 授权范围（Group，NE）
     */
    private String authScope;
    /**
     * 授权对象编号
     */
    private String authObjectId;
    /**
     * 控制项编号
     */
    private String id;
    /**
     * 联合鉴权编号，同一鉴权组的使用相同的编号
     */
    private String comboFunctionId;
    /**
     * ume编号
     */
    private String umeId;
    /**
     * 请求的增量lcs值
     */
    private String value;
    /**
     * 单项鉴权结果状态 ：AGREED(许可)，REJECTED(拒绝)，UNDEFINED(没有找到定义)
     */
    private String state;
    /**
     * 剩余项 ，数值型为当前项的剩余值；开关型恒为“-1”。
     */
    private String availableValue;

}
