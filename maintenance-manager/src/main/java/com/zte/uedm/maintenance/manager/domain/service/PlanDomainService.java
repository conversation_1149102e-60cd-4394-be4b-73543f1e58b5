package com.zte.uedm.maintenance.manager.domain.service;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.manager.application.command.MaintenancePlanCommand;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.MaintenancePlanV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanIdV;
import com.zte.uedm.maintenance.manager.infrastructure.common.bean.NameVersionBean;

import java.util.List;

public interface PlanDomainService {
    /**
     * 根据计划cmd数据查询模板名称版本信息
     */
    List<NameVersionBean> getAllTemplateByPlanCmd(MaintenancePlanCommand maintenancePlanCommand);

    /**
     * 根据计划id查询原始模板
     */
    List<NameVersionBean> getAllOldTemplateByPlanId(String planId);

    /**
     * 根据信息创建计划实体
     */
    MaintenancePlanEntity createPlan(MaintenancePlanV planV) throws UedmException;

    /**
     * 编辑信息得到计划实体
     */
    MaintenancePlanEntity editPlan(MaintenancePlanV planV,MaintenancePlanEntity oldPlan) throws UedmException;

    /**
     *  复制计划得到计划实体
     */
    MaintenancePlanEntity copyPlan(MaintenancePlanV planV,MaintenancePlanEntity oldPlan) throws UedmException;

    /**
     * 检测计划是否可以被删除
     */
    MaintenancePlanEntity checkPlanCanBeDelAndGetMaxVersion(List<MaintenancePlanEntity> planList, PlanIdV planIdV) throws UedmException;

    /**
     * 更新计划状态是检验
     */
    void validateTemplateAndCompensation(MaintenancePlanEntity entity,Integer newStatus) throws UedmException;
}
