package com.zte.uedm.maintenance.manager.infrastructure.client.user;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.maintenance.manager.application.query.MaintenanceUserListQuery;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.UserBean;

import java.util.List;
import java.util.Map;

public interface UserServiceRpc {

    UserBean getUsersInfoByName(String userName, String lang);

    Map<String,UserBean> getAllUser();

    List<String> getAllUserByGroup(String departmentName, String userName, String lang);

    List<UserBean> getAllUserByGroup(String departmentName);

    ResponseBean pageAllUserByGroup(MaintenanceUserListQuery vo);

    List<UserBean> getUsersByGroupName(String departmentName);
}
