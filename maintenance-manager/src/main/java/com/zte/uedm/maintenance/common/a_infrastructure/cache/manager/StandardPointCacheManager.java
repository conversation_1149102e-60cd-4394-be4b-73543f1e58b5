package com.zte.uedm.maintenance.common.a_infrastructure.cache.manager;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.component.caffeine.service.CacheDataProvider;
import com.zte.uedm.component.caffeine.service.CacheMgr;
import com.zte.uedm.component.caffeine.service.impl.CacheBaseManager;
import com.zte.uedm.maintenance.common.a_domain.aggregate.standardpoint.model.entity.StandardPointEntity;
import com.zte.uedm.maintenance.common.a_domain.cache.StandardPointCacheMgr;
import com.zte.uedm.maintenance.common.a_domain.cache.provider.StandardPointCacheDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 标准测点 缓存管理类
 */
@Slf4j
@Component("CACHE_NAME_STANDARD_POINT_INSTANCE")
/* Started by AICoder, pid:80952t763f8e5b814a7b098d71342428be152d81 */
public class StandardPointCacheManager extends CacheBaseManager implements StandardPointCacheMgr<String, StandardPointEntity>,
        CacheMgr<BaseCacheBean> {
    private static final String CACHE_NAME = "CACHE_NAME_STANDARD_POINT_INSTANCE";

    @Autowired
    private StandardPointCacheDataProvider standardPointCacheDataProvider;

    @Override
    public String getCacheName() {
        return CACHE_NAME;
    }

    @Override
    public CacheDataProvider getCacheDataProvider() {
        return standardPointCacheDataProvider;
    }

    @Override
    protected boolean getIsMulti() {
        return true;
    }

    @Override
    public Set<String> init() {
        Set<String> keys = new HashSet<>();
        try {
            keys = super.init();
        } catch (UedmException e) {
            log.error("StandardPointCacheManager init error", e);
        }
        return keys;
    }

    @Override
    public StandardPointEntity getStandardByIdMoc(String id, String moc) throws UedmException {
        if (StringUtils.isAnyBlank(id, moc)) {
            log.warn("getStandardByIdMoc param is blank");
            return null;
        }
        // id + moc 确定一条测点
        return getStandardByMocs(Collections.singleton(moc)).stream()
                .filter(standardPointEntity -> id.equals(standardPointEntity.getId()))
                .findFirst().orElse(null);
    }

    @Override
    public List<StandardPointEntity> getStandardListByIdMoc(Set<String> ids, String moc) throws UedmException {
        // 检查参数是否为空
        if (CollectionUtils.isEmpty(ids) || StringUtils.isEmpty(moc)) {
            log.warn("getStandardByIdMoc param is blank");
            return new ArrayList<>();
        }

        // 获取指定 moc 的标准点列表，并通过流过滤出包含在 ids 中的标准点
        return getStandardByMocs(Collections.singleton(moc)).stream()
                .filter(standardPointEntity -> ids.contains(standardPointEntity.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<StandardPointEntity> getStandardByMocs(Set<String> mocSet) throws UedmException {
        if (CollectionUtils.isEmpty(mocSet)) {
            return Collections.emptyList();
        }
        List<StandardPointEntity> standardPointEntities = queryAll().stream()
                .filter(Objects::nonNull)
                .map(StandardPointEntity.class::cast)
                .filter(standardPointEntity -> StringUtils.isNotBlank(standardPointEntity.getMoc()))
                .filter(standardPointEntity -> mocSet.contains(standardPointEntity.getMoc()))
                .collect(Collectors.toList());
        log.info("getStandardByMocs result size:{}", standardPointEntities.size());
        return standardPointEntities;
    }


    @Override
    public Map<String, List<StandardPointEntity>> getStandardByPointType(Set<String> pointTypeSet) throws UedmException {
        if (CollectionUtils.isEmpty(pointTypeSet)) {
            return new HashMap<>();
        }
        return queryAll().stream()
                .filter(Objects::nonNull)
                .map(StandardPointEntity.class::cast)
                .filter(standardPointEntity -> StringUtils.isNotBlank(standardPointEntity.getPointType()))
                .filter(standardPointEntity -> pointTypeSet.contains(standardPointEntity.getPointType()))
                .collect(Collectors.groupingBy(StandardPointEntity::getPointType));
    }

    @Override
    public List<StandardPointEntity> getStandardByIsExpand(Set<String> idSet, Boolean isExpand) throws UedmException {
        if (CollectionUtils.isEmpty(idSet)) {
            return new ArrayList<>();
        }
        return queryByKeys(idSet).parallelStream()
                .filter(Objects::nonNull)
                .map(StandardPointEntity.class::cast)
                .filter(it -> isExpand == StringUtils.isNotBlank(it.getBelongTo()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Set<String>> getStandardPointExpandMap(Set<String> standardPointIdSet) throws UedmException {
        if (CollectionUtils.isEmpty(standardPointIdSet)) {
            return Collections.emptyMap();
        }
        return queryAll().stream()
                .filter(Objects::nonNull)
                .map(StandardPointEntity.class::cast)
                .filter(standardPointEntity -> StringUtils.isNotBlank(standardPointEntity.getBelongTo()))
                .filter(standardPointEntity -> standardPointIdSet.contains(standardPointEntity.getBelongTo()))
                .collect(Collectors.groupingBy(StandardPointEntity::getId, Collectors.mapping(StandardPointEntity::getBelongTo, Collectors.toSet())));
    }
    @Override
    public List<StandardPointEntity> getStandardById(Set<String> ids ) throws UedmException {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("getStandardById param is blank");
            return null;
        }

        return queryAll().stream()
                .filter(Objects::nonNull)
                .map(StandardPointEntity.class::cast)
                .filter(bean->ids.contains(bean.getId())).collect(Collectors.toList());
    }
}
/* Ended by AICoder, pid:80952t763f8e5b814a7b098d71342428be152d81 */