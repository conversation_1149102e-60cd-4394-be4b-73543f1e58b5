package com.zte.uedm.maintenance.risk.application.executor.impl;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.common.util.ErrorCode.UedmErrorCodeConstants;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionFlowCommand;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import com.zte.uedm.maintenance.risk.application.executor.RiskActivitiService;
import com.zte.uedm.maintenance.workflow.activiti.bean.ExecuteBean;
import com.zte.uedm.maintenance.workflow.activiti.bean.HistoryOperationBean;
import com.zte.uedm.maintenance.workflow.activiti.enums.MalfunctionOperationEnum;
import com.zte.uedm.maintenance.workflow.activiti.enums.MalfunctionOrderStatusEnums;
import com.zte.uedm.maintenance.workflow.activiti.service.MalfunctionActivitiService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.uedm.maintenance.risk.infrastructure.common.RiskConstant.RISK_TEMPLATE_KEY;


@Service
@Slf4j
public class RiskActivitiServiceImpl implements RiskActivitiService {
    @Resource
    private TaskService taskService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private IdentityService identityService;
    @Resource
    private HistoryService historyService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private JsonService jsonService;
    @Resource
    private I18nUtils i18nUtils;
    @Resource
    private DateTimeService dateTimeService;

    private static final String EXECUTE_FIST = "executeFirst";
    private static final String EXECUTE_SECOND = "executeSecond";
    private static final String EXECUTE_INIT = "init";
    private static final String HANGUP_ING_FIRST = "hangupIngFirst";
    private static final String HANGUP_ING_SECOND = "hangupIngSecond";
    private static final String HANGUP_FIRST = "hangupFirst";
    private static final List<String> EXECUTE_NODE_ID = Arrays.asList(EXECUTE_FIST,EXECUTE_SECOND);
    private static final List<String> EXECUTE_NOT_SHOW_ID = Arrays.asList(EXECUTE_INIT,EXECUTE_FIST,EXECUTE_SECOND,HANGUP_ING_FIRST,HANGUP_ING_SECOND);
    private static final String MALFUNCTION_TEMPLATE_KEY = "malfunction";
    private static final String TYPE_KEY = "type";
    private static final String ACTIVITY_TYPE = "userTask";

    private static final Map<String,String> ACTIVITI_NAME_MAP = new HashMap<>();
    private static final String INIT = "拟制中";
    private static final String INIT_OLD = "拟制";
    private static final String SUBMIT = "提交";
    private static final String PENDING = "待处理";
    private static final String REVOKE = "撤销";
    private static final String REJECT = "驳回";
    private static final String SUSPEND = "挂起";
    private static final String SUSPEND_ING = "挂起中";
    private static final String HAND_OVER = "转交";
    private static final String ACCEPT = "接受";
    private static final String PROCESSING = "处理中";
    private static final String CANCEL = "取消";
    private static final String COMPLETE = "完结";

    //操作历史最新的
    private static final List<String> WAIT_RESET_TIME_LIST = new ArrayList<>(Arrays.asList(SUBMIT,REVOKE,REJECT,SUSPEND,HAND_OVER,ACCEPT,CANCEL,COMPLETE));
    private static final List<String> EXECUTE_NOT_SHOW_NAME = Arrays.asList(INIT,PENDING,PROCESSING);

    static {
        ACTIVITI_NAME_MAP.put(INIT,"{\"zh_CN\":\"拟制中\",\"en_US\":\"Drafting\"}");
        ACTIVITI_NAME_MAP.put(INIT_OLD,"{\"zh_CN\":\"拟制中\",\"en_US\":\"Drafting\"}");
        ACTIVITI_NAME_MAP.put(SUBMIT,"{\"zh_CN\":\"提交\",\"en_US\":\"Submit\"}");
        ACTIVITI_NAME_MAP.put(PENDING,"{\"zh_CN\":\"待处理\",\"en_US\":\"Pending\"}");
        ACTIVITI_NAME_MAP.put(REVOKE,"{\"zh_CN\":\"撤销\",\"en_US\":\"Revoke\"}");
        ACTIVITI_NAME_MAP.put(REJECT,"{\"zh_CN\":\"驳回\",\"en_US\":\"Reject\"}");
        ACTIVITI_NAME_MAP.put(SUSPEND,"{\"zh_CN\":\"挂起\",\"en_US\":\"Suspend\"}");
        ACTIVITI_NAME_MAP.put(SUSPEND_ING,"{\"zh_CN\":\"挂起中\",\"en_US\":\"Suspending\"}");
        ACTIVITI_NAME_MAP.put(HAND_OVER,"{\"zh_CN\":\"转交\",\"en_US\":\"Hand Over\"}");
        ACTIVITI_NAME_MAP.put(ACCEPT,"{\"zh_CN\":\"接受\",\"en_US\":\"Accept\"}");
        ACTIVITI_NAME_MAP.put(PROCESSING,"{\"zh_CN\":\"处理中\",\"en_US\":\"Processing\"}");
        ACTIVITI_NAME_MAP.put(CANCEL,"{\"zh_CN\":\"取消\",\"en_US\":\"Cancel\"}");
        ACTIVITI_NAME_MAP.put(COMPLETE,"{\"zh_CN\":\"完结\",\"en_US\":\"Complete\"}");
    }

    @Override
    public String startMalfunctionProcessByProcessKey(String processKey,boolean isNeedSubmit,String nextExecutor) throws UedmException {
        //发起流程 发起人
        String loginUserName = LoginHelper.getLoginUserName();
        return startMalfunctionProcessByProcessKey(processKey, isNeedSubmit, nextExecutor,loginUserName);
    }

    @Override
    public String startMalfunctionProcessByProcessKey(String processKey,boolean isNeedSubmit,String nextExecutor,String loginUserName) throws UedmException {
        identityService.setAuthenticatedUserId(loginUserName);
        Map<String, Object> variables = new HashMap<>();

        //拟制人
        variables.put(MalfunctionOperationEnum.INIT.getOperatorKey(), loginUserName);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey, variables);

        // 自动完成第一个拟制节点并且设置下一个节点，也就是提交人
        Task autoTask = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
        HashMap<String, Object> nextExecutorMap = new HashMap<>();
        nextExecutorMap.put(MalfunctionOperationEnum.SUBMIT.getOperatorKey(), loginUserName);
        taskService.complete(autoTask.getId(),nextExecutorMap);

        //判断是否需要直接提交,需要提交则直接提交,并且指定下一步处理人
        if(isNeedSubmit){
            if(StringUtils.isEmpty(nextExecutor)){
                throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,"when need submit,the nextExecutor can not be null.");
            }
            Task currentTask = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
            Map<String, Object> vars = new HashMap<>();
            vars.put(MalfunctionOperationEnum.EXECUTE_FIRST.getOperatorKey(), nextExecutor);
            vars.put(TYPE_KEY, MalfunctionOperationEnum.SUBMIT.getType());
            taskService.complete(currentTask.getId(), vars);
        }

        return processInstance.getId();
    }

    @Override
    public String submitOrder(String processInstanceId,String nextExecutor) throws UedmException {
        if(StringUtils.isEmpty(nextExecutor)){
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,"when need submit,the nextExecutor can not be null.");
        }
        //提交流程
        Task currentTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是提交或者拟制流程
        checkCurrentTask(currentTask,SUBMIT,INIT,INIT_OLD);

        Map<String, Object> vars = new HashMap<>();

        //当前流程拟制流程
        if(INIT.equals(currentTask.getName()) || INIT_OLD.equals(currentTask.getName())){
            vars.put(MalfunctionOperationEnum.SUBMIT.getOperatorKey(), currentTask.getAssignee());
            //完成拟制
            taskService.complete(currentTask.getId(),vars);

            Task submitTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

            vars.clear();
            vars.put(MalfunctionOperationEnum.EXECUTE_FIRST.getOperatorKey(), nextExecutor);
            vars.put(TYPE_KEY, MalfunctionOperationEnum.SUBMIT.getType());

            //完成提交
            taskService.complete(submitTask.getId(),vars);
        }
        //当前为提交流程
        else if(SUBMIT.equals(currentTask.getName())){
            vars.put(MalfunctionOperationEnum.EXECUTE_FIRST.getOperatorKey(), nextExecutor);
            vars.put(TYPE_KEY, MalfunctionOperationEnum.SUBMIT.getType());
            //完成提交
            taskService.complete(currentTask.getId(),vars);
        }

        return processInstanceId;
    }



    @Override
    public String rejectOrder(String processInstanceId,String reason,String attachment) throws UedmException {
        //完成处理流程
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是待处理处理中流程
        checkCurrentTask(executeTask,PENDING,PROCESSING);

        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.REJECT.getType());
        vars.put(MalfunctionOperationEnum.REJECT.getOperatorKey(), executeTask.getAssignee());
        taskService.complete(executeTask.getId(), vars);

        //驳回流程
        Task rejectTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //完成驳回流程增加附件原因
        setCommentAndComplete(rejectTask.getId(),processInstanceId,attachment,reason);

        //获取下一步处理人
        Task afterTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        return afterTask.getAssignee();
    }

    @Override
    public void revokeOrder(String processInstanceId) throws UedmException {
        //更改执行流程的处理人为自己
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        taskService.setAssignee(executeTask.getId(),LoginHelper.getLoginUserName());
        //当前流程必须是待处理流程
        checkCurrentTask(executeTask,PENDING);

        //完成处理流程 走到撤销流程
        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.REVOKE.getType());
        vars.put(MalfunctionOperationEnum.REVOKE.getOperatorKey(), LoginHelper.getLoginUserName());
        taskService.complete(executeTask.getId(), vars);

        //完成撤销流程 走到拟制流程
        Task rejectTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        taskService.complete(rejectTask.getId());
    }

    @Override
    public void suspendProcessInstanceById(String processInstanceId,String reason,String attachment) throws UedmException {
        //完成处理流程进入挂起流程，并且设置挂起流程的执行人
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是待处理处理中流程
        checkCurrentTask(executeTask,PENDING,PROCESSING);

        String assignee = executeTask.getAssignee();
        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.HANGUP.getType());
        vars.put(MalfunctionOperationEnum.HANGUP.getOperatorKey(), assignee);
        taskService.complete(executeTask.getId(), vars);

        //挂起流程
        Task hangupTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        String taskDefinitionKey = hangupTask.getTaskDefinitionKey();
        ExecuteBean executeBean = new ExecuteBean();
        executeBean.setAttachment(attachment);
        executeBean.setReason(reason);
        String info = jsonService.objectToJson(executeBean);
        taskService.addComment(hangupTask.getId(),processInstanceId,info);
        Map<String, Object> var2 = new HashMap<>();
        String key = HANGUP_FIRST.equals(taskDefinitionKey) ? MalfunctionOperationEnum.HANGUP_ING_FIRST.getOperatorKey() : MalfunctionOperationEnum.HANGUP_ING_SECOND.getOperatorKey();
        var2.put(key, assignee);
        //完成流程
        taskService.complete(hangupTask.getId(),var2);
        //完成挂起流程并增加附件原因

        //挂起流程实例
//        runtimeService.suspendProcessInstanceById(processInstanceId);
    }

    @Override
    public void acceptOrder(String processInstanceId,String nextExecutor,String reason,String attachment) throws UedmException {
        if(StringUtils.isEmpty(nextExecutor)){
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,"when need accept,the nextExecutor can not be null.");
        }
        //完成处理流程进入接受流程，并且设置接受流程的执行人
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是待处理流程
        checkCurrentTask(executeTask,PENDING);

        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.ACCEPT.getType());
        vars.put(MalfunctionOperationEnum.ACCEPT.getOperatorKey(), executeTask.getAssignee());
        taskService.complete(executeTask.getId(), vars);

        //接受流程
        Task acceptTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //附件
        setComment(acceptTask.getId(),processInstanceId,attachment,reason);

        //完成接受流程
        Map<String, Object> varsOne = new HashMap<>();
        varsOne.put(MalfunctionOperationEnum.EXECUTE_SECOND.getOperatorKey(), nextExecutor);
        taskService.complete(acceptTask.getId(),varsOne);
    }

    @Override
    public void handOver(String processInstanceId,String nextExecutor,String reason,String attachment) throws UedmException {
        if(StringUtils.isEmpty(nextExecutor)){
            throw new UedmException(UedmErrorCodeConstants.PARAMETER_IS_MUST_INPUT,"when need handOver,the nextExecutor can not be null.");
        }
        //处理流程
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是待处理处理中流程
        checkCurrentTask(executeTask,PENDING,PROCESSING);

        //完成处理流程，并且设置转交流程的执行人
        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.HANDOVER.getType());
        vars.put(MalfunctionOperationEnum.HANDOVER.getOperatorKey(), executeTask.getAssignee());
        taskService.complete(executeTask.getId(), vars);

        //转交流程
        Task handoverTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //附件
        setComment(handoverTask.getId(),processInstanceId,attachment,reason);

        //如果处理流程是待处理  完成转交流程，更新待处理人，否则处理中更新处理中处理人
        Map<String, Object> params = new HashMap<>();
        if(EXECUTE_FIST.equals(executeTask.getTaskDefinitionKey())){
            params.put(MalfunctionOperationEnum.EXECUTE_FIRST.getOperatorKey(),nextExecutor);
        }
        else {
            params.put(MalfunctionOperationEnum.EXECUTE_SECOND.getOperatorKey(),nextExecutor);
        }
        taskService.complete(handoverTask.getId(),params);
    }

    @Override
    public void cancel(String processInstanceId,String reason,String attachment) throws UedmException {
        //完成处理流程进入取消流程，并且设置转交流程的执行人
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //当前流程必须是待处理流程
        checkCurrentTask(executeTask,PENDING);

        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.CANCEL.getType());
        vars.put(MalfunctionOperationEnum.CANCEL.getOperatorKey(), executeTask.getAssignee());
        taskService.complete(executeTask.getId(), vars);

        //取消流程
        Task handoverTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        //完成取消流程 结束
        setCommentAndComplete(handoverTask.getId(),processInstanceId,attachment,reason);
    }

    @Override
    public void complete(String processInstanceId,String reason,String attachment) throws UedmException {
        //完成处理流程进入取消流程，并且设置转交流程的执行人
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        //当前流程必须是处理中流程
        checkCurrentTask(executeTask,PROCESSING);

        Map<String, Object> vars = new HashMap<>();
        vars.put(TYPE_KEY, MalfunctionOperationEnum.OVER.getType());
        vars.put(MalfunctionOperationEnum.OVER.getOperatorKey(), executeTask.getAssignee());
        taskService.complete(executeTask.getId(), vars);

        //完结流程
        Task handoverTask = taskService
                .createTaskQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        //完成完结流程 结束
        setCommentAndComplete(handoverTask.getId(),processInstanceId,attachment,reason);
    }

    @Override
    public List<String> getWaitExecuteProcessInstanceByUser(String user) {
        //获取用户待处理的任务
        List<Task> taskList = taskService.createTaskQuery().taskAssignee(user).list();

        return getProcessInstanceId(taskList);
    }

    @Override
    public List<String> getDoneProcessIdsByUserAndType(String user) {
        //获取用户已处理的任务
        List<HistoricTaskInstance> taskList  = historyService
                .createHistoricTaskInstanceQuery()
                .taskAssignee(user)
                .finished()
                .list();
        return getProcessInstanceId(taskList);
    }

    @Override
    public List<HistoryOperationBean> getTimelineByProcessInstanceId(String processInstanceId){
        return getTimelineByProcessInstanceId(processInstanceId,true);
    }

    @Override
    public List<HistoryOperationBean> getTimelineByProcessInstanceId(String processInstanceId,Boolean isNeedExecuteNode){
        String language = LoginHelper.getLanguage();

        if(StringUtils.isEmpty(processInstanceId)){
            return new ArrayList<>();
        }

        //userTask所有节点
        List<HistoricActivityInstance> allTaskList = historyService
                .createHistoricActivityInstanceQuery()
                //流程id对相应的任务历史
                .processInstanceId(processInstanceId)
                //userTask类型
                .activityType(ACTIVITY_TYPE)
                //开始时间排序
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();
        log.info("allTaskList={}",allTaskList);

        //过滤
        if(!isNeedExecuteNode){
            //获取最新任务
            Task currentTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
            log.info("currentTask={}",currentTask);

            List<HistoricActivityInstance> finalAllTaskList = allTaskList;
            allTaskList = allTaskList.stream().filter(task -> filter(currentTask,task, finalAllTaskList)).collect(Collectors.toList());
            log.info("filter allTask={}",allTaskList);
        }

        //转换bean
        List<HistoryOperationBean> filterTask = allTaskList
                .stream()
                .map(task -> {
                    HistoryOperationBean historyOperationBean = new HistoryOperationBean();
                    historyOperationBean.setOperationName(i18nUtils.getMapFieldByLanguageOption(ACTIVITI_NAME_MAP.get(task.getActivityName()),language));
                    historyOperationBean.setHandlerName(task.getAssignee());
                    historyOperationBean.setActivityName(task.getActivityName());
                    historyOperationBean.setDealTime(getStrTime(task.getStartTime()));
                    historyOperationBean.setDealEndTime(getStrTime(task.getEndTime()));
                    setReasonAndAttachment(historyOperationBean,task.getTaskId());
                    historyOperationBean.setShowOther(getShowOther(task.getActivityName()));
                    return historyOperationBean;
                })
                .collect(Collectors.toList());
        log.info("filterTask={}",filterTask);

       if(!isNeedExecuteNode){
           //因为排除了拟制中待处理处理中，处理任务节点需要重新设置任务开始时间
           for (int i = 0; i < filterTask.size(); i++) {
               HistoryOperationBean task = filterTask.get(i);
               if(WAIT_RESET_TIME_LIST.contains(task.getActivityName()) && i != filterTask.size() - 1){
                   HistoryOperationBean before = filterTask.get(i + 1);
                   task.setDealTime(before.getDealEndTime());
               }
           }
       }
        return filterTask;
    }

    private Boolean getShowOther(String nodeName){
        return !EXECUTE_NOT_SHOW_NAME.contains(nodeName);
    }

    @Override
    public MalfunctionOrderStatusEnums getCurrentActivityStatus(String processInstanceId) throws UedmException {
        //完成处理流程进入取消流程，并且设置转交流程的执行人
        Task executeTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();

        if(executeTask == null){
            throw new UedmException(-1,"current task not found,processInstanceId = " + processInstanceId);
        }
        return MalfunctionOrderStatusEnums.getStatusByActivitiName(executeTask.getTaskDefinitionKey());
    }

    /**
     * 检验任务状态
     * @param task 任务
     * @param nodeProcess 期望的状态
     */
    public void checkCurrentTask(Task task,String... nodeProcess) throws UedmException {
        if(task == null){
            throw new UedmException(-1,"current task not found");
        }

        //如果不是期望的，异常
        if(Arrays.stream(nodeProcess).noneMatch(nodeName -> nodeName.equals(task.getName()))){
            log.error("current task can not support.expect={},actual={}",nodeProcess,task.getName());
            throw new UedmException(-1,"current task can not support this operation.");
        }
    }

    public Boolean filter(Task currentTask,HistoricActivityInstance taskFlag,List<HistoricActivityInstance> allTaskList){
        //任务已完成，排除所有拟制中 待处理 处理中
        if(currentTask == null){
            return !EXECUTE_NOT_SHOW_ID.contains(taskFlag.getActivityId());
        }
        //任务未完成，当前任务一定是在拟制中 待处理 处理中，保留第一个节点(处理节点)或者（其他非处理节点)
        else{
            return allTaskList.indexOf(taskFlag) == 0 || !EXECUTE_NOT_SHOW_ID.contains(taskFlag.getActivityId());
        }
    }

    public String getStrTime(Date date){
        if(null == date){
            return null;
        }

        return dateTimeService.getStrTime(date.getTime());
    }

    private void setReasonAndAttachment(HistoryOperationBean historyOperationBean, String taskId){
        try{
            List<Comment> comments = taskService.getTaskComments(taskId);
            if (comments.size() > 0) {
                String fullMessage = comments.get(0).getFullMessage();
                ExecuteBean executeBean = jsonService.jsonToObject(fullMessage, ExecuteBean.class);
                historyOperationBean.setDealOpinion(executeBean.getReason());
                historyOperationBean.setAttachment(executeBean.getAttachment());
            }
        }catch (Exception e){
            log.error("setReasonAndAttachment error,taskId = {}",taskId);
        }
    }

    private List<String> getProcessInstanceId(List<? extends TaskInfo> taskList){
        //获取所有的风险流程定义
        List<ProcessDefinition> allDefinition = repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionKey(RISK_TEMPLATE_KEY)
                .list();
        List<String> allDefinitionId = allDefinition
                .stream()
                .map(ProcessDefinition::getId)
                .collect(Collectors.toList());

        return taskList
                .stream()
                //任务必须是故障流程里面 而且必须是处理流程（状态节点）非操作流程 有特殊key标识
                .filter(task -> allDefinitionId.contains(task.getProcessDefinitionId())
                        && EXECUTE_NODE_ID.contains(task.getTaskDefinitionKey()))
                .map(TaskInfo::getProcessInstanceId)
                .distinct()
                .collect(Collectors.toList());
    }

    private void setComment(String taskId,String processInstanceId,String attachment,String reason) throws UedmException {
        //流程增加附件原因
        ExecuteBean executeBean = new ExecuteBean();
        executeBean.setAttachment(attachment);
        executeBean.setReason(reason);
        String info = jsonService.objectToJson(executeBean);
        taskService.addComment(taskId,processInstanceId,info);
    }

    private void setCommentAndComplete(String taskId,String processInstanceId,String attachment,String reason) throws UedmException {
        //流程增加附件原因
        ExecuteBean executeBean = new ExecuteBean();
        executeBean.setAttachment(attachment);
        executeBean.setReason(reason);
        String info = jsonService.objectToJson(executeBean);
        taskService.addComment(taskId,processInstanceId,info);

        //完成流程
        taskService.complete(taskId);
    }

    @Override
    public void checkHangupIng(MalfunctionFlowCommand malfunctionFlowCommand){
        try {
            String workflowId = malfunctionFlowCommand.getWorkflowId();
            //提交流程
            Task currentTask = taskService.createTaskQuery().processInstanceId(workflowId).singleResult();
            //当前流程是挂起中 则需要完成该流程
            checkCurrentTask(currentTask,SUSPEND_ING);
            Map<String, Object> vars = new HashMap<>();
            String taskDefinitionKey = currentTask.getTaskDefinitionKey();
            String key = HANGUP_ING_FIRST.equals(taskDefinitionKey) ? MalfunctionOperationEnum.EXECUTE_FIRST.getOperatorKey() : MalfunctionOperationEnum.EXECUTE_SECOND.getOperatorKey();
            vars.put(key, currentTask.getAssignee());
            taskService.complete(currentTask.getId(), vars);
        } catch (UedmException e) {
            log.info("node name is not hangupIng,{}",e.getMessage());
        }
    }
}
