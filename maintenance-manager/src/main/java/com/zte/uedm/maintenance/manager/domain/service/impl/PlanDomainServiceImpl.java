package com.zte.uedm.maintenance.manager.domain.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.maintenance.manager.application.command.MaintenanceObjectCommand;
import com.zte.uedm.maintenance.manager.application.command.MaintenancePlanCommand;
import com.zte.uedm.maintenance.manager.application.executor.MaintenanceTaskCommandService;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenanceObjectEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.ObjectFactory;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.MaintenancePlanV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanIdV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanNameV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanObjectV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository.MaintenanceObjectRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository.MaintenancePlanRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetask.model.MaintenanceTaskEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetask.repository.MaintenanceTaskRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.model.MaintenanceTemplateEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.repository.MaintenanceTemplateRepository;
import com.zte.uedm.maintenance.manager.domain.service.PlanDomainService;
import com.zte.uedm.maintenance.manager.domain.shared.enums.MaintenancePlanStatusEnums;
import com.zte.uedm.maintenance.manager.domain.shared.enums.PlanOperationErrorEnums;
import com.zte.uedm.maintenance.manager.infrastructure.common.GlobalConst;
import com.zte.uedm.maintenance.manager.infrastructure.common.bean.NameVersionBean;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.po.MaintenanceObjectPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.po.MaintenancePlanPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenancetask.po.MaintenanceTaskPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlanDomainServiceImpl implements PlanDomainService {

    @Resource
    private JsonService jsonService;
    @Resource
    private MaintenanceObjectRepository maintenanceObjectRepository;
    @Resource
    private MaintenanceTemplateRepository maintenanceTemplateRepository;
    @Resource
    private MaintenancePlanRepository maintenancePlanRepository;
    @Resource
    private I18nUtils i18nUtils;
    @Resource
    private DateTimeService dateTimeService;
    @Resource
    private MaintenanceTaskRepository maintenanceTaskRepository;
    @Resource
    private ObjectFactory objectFactory;
    @Resource
    private MaintenanceTaskCommandService maintenanceTaskCommandService;

    @Override
    public List<NameVersionBean> getAllTemplateByPlanCmd(MaintenancePlanCommand maintenancePlanCommand) {
        List<String> allTemplateIdList = maintenancePlanCommand
                .getMaintenanceObject()
                .stream()
                .map(MaintenanceObjectCommand::getMaintenanceTemplate)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(IdNameBean::getId)
                .distinct()
                .collect(Collectors.toList());

        return getTemplateNameVersionByIds(allTemplateIdList);
    }

    @Override
    public List<NameVersionBean> getAllOldTemplateByPlanId(String planId){
        //先查对象
        List<MaintenanceObjectPO> maintenanceObject = maintenanceObjectRepository.getMaintenanceObjectByPlanId(planId);
        List<String> allTemplateIdList = maintenanceObject
                .stream()
                .map(obj -> {
                    List<IdNameBean> template = new ArrayList<>();
                    try {
                        template = jsonService.jsonToObject(obj.getTemplate(), List.class, IdNameBean.class);
                    } catch (UedmException e) {
                        log.error("obj template convert error.",e);
                    }
                    return template;
                })
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(IdNameBean::getId)
                .distinct()
                .collect(Collectors.toList());

        return getTemplateNameVersionByIds(allTemplateIdList);
    }

    @Override
    public MaintenancePlanEntity createPlan(MaintenancePlanV maintenancePlanV) throws UedmException {
        //名称校验
        PlanNameV planNameV = new PlanNameV(maintenancePlanV.getName());
        nameCheck(planNameV,null);

        MaintenancePlanEntity maintenancePlan = new MaintenancePlanEntity();

        //通用属性copy
        BeanUtils.copyProperties(maintenancePlanV,maintenancePlan);

        maintenancePlan.setId(IdWorker.getIdStr());
        maintenancePlan.setBaseVersionId(maintenancePlan.getId());
        maintenancePlan.setVersion(GlobalConst.INIT_VERSION);

        //执行人 多个 序列化为json
        String executor = jsonService.objectToJson(maintenancePlanV.getExecutor());
        maintenancePlan.setExecutor(executor);
        maintenancePlan.setStatus(MaintenancePlanStatusEnums.NOT_ENABLED.getStatus());
        maintenancePlan.setCreator(LoginHelper.getLoginUserName());
        maintenancePlan.setGmtCreate(dateTimeService.getCurrentTime());

        //计划执行规则
        String executeRole = jsonService.objectToJson(maintenancePlanV.getExecuteRule());
        maintenancePlan.setExecuteRule(executeRole);

        //提交数据排除id的json
        maintenancePlan.setSubmitJson(getSubmitJson(maintenancePlanV));

        //维保对象
        PlanObjectV planObjectV = new PlanObjectV(maintenancePlanV.getMaintenanceObject());
        List<MaintenanceObjectEntity> batchByPlanObject = objectFactory.createBatchByPlanObject(planObjectV);
        maintenancePlan.setPlanObject(batchByPlanObject);

        return maintenancePlan;
    }

    @Override
    public MaintenancePlanEntity editPlan(MaintenancePlanV maintenancePlanV,MaintenancePlanEntity oldPlan) throws UedmException {
        //名称校验
        PlanNameV planNameV = new PlanNameV(maintenancePlanV.getName());
        PlanIdV planIdV = new PlanIdV(maintenancePlanV.getId());
        nameCheck(planNameV,planIdV);

        //查询原始计划不能没有
        oldPlan = oldPlan == null ? maintenancePlanRepository.getById(planIdV) : oldPlan;
        if(oldPlan == null){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_NOT_EXIST);
        }

        //查询最新计划
        MaintenancePlanEntity maxVersionPlan = maintenancePlanRepository.getMaxVersionByBaseVersionId(oldPlan.getBaseVersionId());
        maxVersionPlan.checkEditStatus(i18nUtils);

        MaintenancePlanEntity maintenancePlan = new MaintenancePlanEntity();

        //通用属性copy
        BeanUtils.copyProperties(maintenancePlanV,maintenancePlan);

        maintenancePlan.setId(IdWorker.getIdStr());
        maintenancePlan.setBaseVersionId(oldPlan.getBaseVersionId());
        maintenancePlan.setCreator(oldPlan.getCreator());
        maintenancePlan.setGmtCreate(oldPlan.getGmtCreate());
        maintenancePlan.setStatus(oldPlan.getStatus());
        maintenancePlan.setGmtModified(dateTimeService.getCurrentTime());
        maintenancePlan.setUpdater(LoginHelper.getLoginUserName());
        maintenancePlan.setLastTaskGenerateTime(null);

        //执行人 多个 序列化为json
        String executor = jsonService.objectToJson(maintenancePlanV.getExecutor());
        maintenancePlan.setExecutor(executor);

        //计划执行规则
        String executeRole = jsonService.objectToJson(maintenancePlanV.getExecuteRule());
        maintenancePlan.setExecuteRule(executeRole);

        //内容存在修改则插入维保计划,版本加1
        String submitJson = getSubmitJson(maintenancePlanV);
        if(!oldPlan.getSubmitJson().equals(submitJson)){
            maintenancePlan.setId(IdWorker.getIdStr());
            maintenancePlan.setVersion(maxVersionPlan.getVersion() + 1);
            maintenancePlan.setSubmitJson(submitJson);
        }
        //否则只更新当前版本的数据
        else{
            maintenancePlan.setId(oldPlan.getId());
            maintenancePlan.setVersion(oldPlan.getVersion());
            maintenancePlan.setSubmitJson(submitJson);
        }

        //维保对象
        PlanObjectV planObjectV = new PlanObjectV(maintenancePlanV.getMaintenanceObject());
        List<MaintenanceObjectEntity> batchByPlanObject = objectFactory.createBatchByPlanObject(planObjectV);
        maintenancePlan.setPlanObject(batchByPlanObject);

        return maintenancePlan;
    }

    @Override
    public MaintenancePlanEntity copyPlan(MaintenancePlanV maintenancePlanV,MaintenancePlanEntity oldPlan) throws UedmException {
        //名称校验
        PlanIdV planIdV = new PlanIdV(maintenancePlanV.getId());
        PlanNameV planNameV = new PlanNameV(maintenancePlanV.getName());
        nameCheck(planNameV,planIdV);

        //查询原始计划不能没有
        oldPlan = oldPlan == null ? maintenancePlanRepository.getById(planIdV) : oldPlan;
        if(oldPlan == null) {
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_NOT_EXIST);
        }

        MaintenancePlanEntity maintenancePlan = new MaintenancePlanEntity();

        //通用属性copy
        BeanUtils.copyProperties(maintenancePlanV,maintenancePlan);

        //执行人 多个 序列化为json
        String executor = jsonService.objectToJson(maintenancePlanV.getExecutor());
        maintenancePlan.setExecutor(executor);

        //计划执行规则
        String executeRole = jsonService.objectToJson(maintenancePlanV.getExecuteRule());
        maintenancePlan.setExecuteRule(executeRole);

        //名称不能冲突
        if(maintenancePlan.getName().equals(oldPlan.getName())){
            maintenancePlan.setName(maintenancePlan.getName() + "-copy-" + RandomStringUtils.randomAlphabetic(5));
        }

        //单独设置的信息
        maintenancePlan.setId(IdWorker.getIdStr());
        maintenancePlan.setBaseVersionId(maintenancePlan.getId());
        maintenancePlan.setVersion(GlobalConst.INIT_VERSION);
        maintenancePlan.setStatus(MaintenancePlanStatusEnums.NOT_ENABLED.getStatus());
        maintenancePlan.setCreator(LoginHelper.getLoginUserName());
        maintenancePlan.setGmtCreate(dateTimeService.getCurrentTime());
        maintenancePlan.setUpdater(null);
        maintenancePlan.setGmtModified(null);

        //提交数据排除id的json
        maintenancePlan.setSubmitJson(getSubmitJson(maintenancePlanV));

        //维保对象
        PlanObjectV planObjectV = new PlanObjectV(maintenancePlanV.getMaintenanceObject());
        List<MaintenanceObjectEntity> batchByPlanObject = objectFactory.createBatchByPlanObject(planObjectV);
        maintenancePlan.setPlanObject(batchByPlanObject);

        return maintenancePlan;
    }

    @Override
    public MaintenancePlanEntity checkPlanCanBeDelAndGetMaxVersion(List<MaintenancePlanEntity> planList,PlanIdV planIdV) throws UedmException {
        //没有找到 不能删除
        if(CollectionUtils.isEmpty(planList)){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_NOT_EXIST);
        }

        List<String> planIdList = planList.stream().map(MaintenancePlanEntity::getId).collect(Collectors.toList());

        //如果最新计划是不是未启用不能删除
        MaintenancePlanEntity maxVersionPlan = planList
                .stream()
                .min((o1, o2) -> o2.getVersion().compareTo(o1.getVersion()))
                .orElse(new MaintenancePlanEntity());
        maxVersionPlan.checkDeleteStatus(i18nUtils);

        //如果最新计划不是原始计划，不允许修改
        if(!planIdV.getId().equals(maxVersionPlan.getId())){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_NEED_FRESH);
        }

        //有关联任务，不能删除
        MaintenanceTaskEntity task = maintenanceTaskRepository.getOneTaskByPlanIdList(planIdList);
        if(task != null){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_HAVE_RELATED);
        }

        return maxVersionPlan;
    }

    @Override
    public void validateTemplateAndCompensation(MaintenancePlanEntity entity, Integer newStatus) throws UedmException {
        if(entity == null){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.PLAN_NEED_FRESH);
        }

        //当操作启用的时候，才进行校验
        if (MaintenancePlanStatusEnums.ENABLED.getStatus().equals(newStatus)) {
            if (!maintenanceObjectRepository.existTemplate(entity.getId())) {
                throw new UedmException(-1, "The template does not exist.");
            }
            //编辑后或者仅仅暂停后启用不需要补偿
            if(entity.getLastTaskGenerateTime() == null){
                MaintenancePlanPO maintenancePlanPO = new MaintenancePlanPO();
                BeanUtils.copyProperties(entity,maintenancePlanPO);
                List<MaintenanceTaskPO> maintenanceTaskPOS = maintenanceTaskCommandService
                        .taskCompensation(new ArrayList<>(Collections.singletonList(maintenancePlanPO)));
                log.debug("create task : {}", maintenanceTaskPOS);
            }
        }
    }


    private void nameCheck(PlanNameV planNameV,PlanIdV planIdV) throws UedmException {
        MaintenancePlanEntity planEntity = maintenancePlanRepository.findMaintenancePlanEntity(planNameV, planIdV);

        if(planEntity != null){
            throw throwExceptionByOperationEnums(PlanOperationErrorEnums.NAME_IS_EXITS);
        }
    }

    private UedmException throwExceptionByOperationEnums(PlanOperationErrorEnums planOperationErrorEnums){
        Integer code = planOperationErrorEnums.getCode();
        String msg = planOperationErrorEnums.getMsg();
        String language = LoginHelper.getLanguage();
        return new UedmException(code,i18nUtils.getMapFieldByLanguageOption(msg,language));
    }

    private String getSubmitJson(MaintenancePlanV submit) throws UedmException {
        submit.setId(null);
        String submitJson = jsonService.objectToJson(submit);
        return SecureUtil.sha256(submitJson);
    }

    private List<NameVersionBean> getTemplateNameVersionByIds(List<String> allTemplateIdList){
        List<MaintenanceTemplateEntity> allTemplate = CollectionUtils.isEmpty(allTemplateIdList) ? new ArrayList<>() :
                maintenanceTemplateRepository.listByIds(allTemplateIdList);

        return allTemplate
                .stream()
                .map(template -> {
                    NameVersionBean nameVersionBean = new NameVersionBean();
                    nameVersionBean.setName(template.getName());
                    nameVersionBean.setVersion(template.getVersion());
                    return nameVersionBean;
                })
                .collect(Collectors.toList());
    }
}
