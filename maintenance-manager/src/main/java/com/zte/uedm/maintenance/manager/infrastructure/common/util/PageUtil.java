package com.zte.uedm.maintenance.manager.infrastructure.common.util;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @ Author     ：10260977
 * @ Date       ：10:13 2020/12/18
 * @ Description：分页公共类
 * @ Modified By：
 * @ Version: 1.0
 */
public class PageUtil
{

    public static <T> List<T> getPageList(List<T> list, Integer pageNo, Integer pageSize)
    {
        if (CollectionUtils.isEmpty(list))
        {
            return Collections.emptyList();
        }

        if (pageNo == null || pageSize == null)
        {
            return list;
        }

        List<T> pageList;
        int total = list.size();
        int beginIndex = pageSize * (pageNo - 1);
        int endIndex = pageSize * pageNo;
        if (endIndex <= total - 1)
        {
            pageList = list.subList(beginIndex, endIndex);
        }
        else
        {
            pageList = list.subList(beginIndex, total);
        }
        return pageList;
    }
}
