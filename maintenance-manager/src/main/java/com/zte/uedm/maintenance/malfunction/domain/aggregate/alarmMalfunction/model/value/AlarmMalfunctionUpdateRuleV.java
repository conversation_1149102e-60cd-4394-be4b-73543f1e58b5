package com.zte.uedm.maintenance.malfunction.domain.aggregate.alarmMalfunction.model.value;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class AlarmMalfunctionUpdateRuleV {

    /**
     * 规则ID（必填）
     */
    @ApiModelProperty("规则ID")
    private String id;

    /**
     * 规则名称（必填）
     */
    @ApiModelProperty("规则名称")
    private String name;

    /**
     * 描述（非必填）
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 状态  1启用   2停用（必填）
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 监控对象类型，（非必填）
     */
    @ApiModelProperty("监控对象类型")
    private List<String> moc;

    /**
     * 告警码路径，回写展示（必填）
     */
    @ApiModelProperty("告警码路径，回写展示")
    private String alarmCodePath;

    /**
     * 告警码（必填）
     */
    @ApiModelProperty("告警码")
    private List<String> alarmCode;

    /**
     * 告警对象id(非必填)
     */
    @ApiModelProperty("告警对象id")
    private List<String> alarmMoId;

    /**
     * 告警对象id路径，回写展示(非必填)
     */
    @ApiModelProperty("告警对象id路径，回写展示")
    private List<List<String>> alarmMoPath;

    /**
     * 告警等级（非必填）
     */
    @ApiModelProperty("告警等级")
    private List<Integer> alarmLevel;


    /**
     * 告警持续时长最低值(非必填)
     */
    @ApiModelProperty("告警持续时长最低值")
    private String miniAlarmDuration;

    /**
     * 时间限制(必填)
     */
    @ApiModelProperty("时间限制")
    private String timeLimit;

    /**
     * 处理人（必填）
     */
    @ApiModelProperty("处理人")
    private String handler;

    /**
     * 影响度(0:低;1:中;2:高)(必填)
     */
    @ApiModelProperty("影响度")
    private String effectLevel;


    /**
     * 紧急度(0:低;1:中;2:高)(必填)
     */
    @ApiModelProperty("紧急度")
    private String emergencyLevel;

    /**
     * 故障级别(0:极地;1:低;2:中;3:高;4:极高)(必填)
     */
    @ApiModelProperty("故障级别")
    private String malfunctionLevel;
}
