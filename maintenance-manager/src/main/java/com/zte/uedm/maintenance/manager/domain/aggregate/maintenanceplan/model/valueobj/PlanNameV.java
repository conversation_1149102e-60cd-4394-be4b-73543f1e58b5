package com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj;

import org.apache.commons.lang3.StringUtils;

public class PlanNameV {
    /**
     * 用户名
     */
    private final String name;

    public PlanNameV(String name) {
        if(StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("计划名称不能为空");
        }
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String toString() {
        return name;
    }
}
