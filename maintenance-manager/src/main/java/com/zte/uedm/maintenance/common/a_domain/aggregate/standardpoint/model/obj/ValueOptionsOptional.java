package com.zte.uedm.maintenance.common.a_domain.aggregate.standardpoint.model.obj;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@Getter
@Setter
@ToString
/* Started by AICoder, pid:2231fq654cv8e6114ba40b3a501611227854c1ff */
public class ValueOptionsOptional {

    private String id;
    /**
     * {"zh_CN":"xxxx","en_US":"xxxx"}
     */
    private String name;
    /**
     * {"zh_CN":"xxxx","en_US":"xxxx"}
     */
    private String description;
    /**
     * 顺序
     */
    private Integer sequence;
    /**
     * 数据源
     */
    private List<String> type;
}


/* Ended by AICoder, pid:2231fq654cv8e6114ba40b3a501611227854c1ff */
