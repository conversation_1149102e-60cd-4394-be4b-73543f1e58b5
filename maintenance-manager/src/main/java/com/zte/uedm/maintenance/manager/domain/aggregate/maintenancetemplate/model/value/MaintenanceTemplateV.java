package com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.model.value;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class MaintenanceTemplateV {
    /**
     * 维保模板id
     */
    private String id;

    /**
     * 版本，默认为1向后递增
     */
    private Integer version;

    /**
     * 基础版id，通过此id可以找到所有历史版本
     */
    private String baseVersionId;

    /**
     * 维保模板名称
     */
    private String name;

    /**
     * 维保对象类型  监控对象为moc，监控设备为moduleId
     */
    private String maintenanceObjType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private String gmtModified;

    private List<MaintenanceItemV> itemList;

}
