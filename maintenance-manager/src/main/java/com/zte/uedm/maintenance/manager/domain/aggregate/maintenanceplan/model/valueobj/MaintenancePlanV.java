package com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj;

import com.zte.uedm.maintenance.manager.interfaces.web.bean.TaskRuleBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class MaintenancePlanV {
    @ApiModelProperty("维保计划id，编辑时需要")
    private String id;

    @ApiModelProperty("维保计划名称")
    private String name;

    @ApiModelProperty("维保计划类型1日常维保 2临时维保 3健康检查")
    private Integer maintenancePlanType;

    @ApiModelProperty("责任组")
    private String responsibleDepartment;

    @ApiModelProperty("责任人一个")
    private String responsiblePerson;

    @ApiModelProperty("执行组")
    private String executeDepartment;

    @ApiModelProperty("执行人多个")
    private List<String> executor = new ArrayList<>();

    @ApiModelProperty("计划开始时间")
    private String planBeginTime;

    @ApiModelProperty("计划结束时间")
    private String planEndTime;

    @ApiModelProperty("提醒方式")
    private Integer reminderMode;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("执行规则")
    private TaskRuleBean executeRule;

    @ApiModelProperty("维保对象")
    private List<MaintenanceObjectV> maintenanceObject;
}
