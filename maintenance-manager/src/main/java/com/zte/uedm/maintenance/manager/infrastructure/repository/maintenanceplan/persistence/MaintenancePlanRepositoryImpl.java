package com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.persistence;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zte.uedm.maintenance.common.util.SqlInjectionUtil;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanNameV;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository.MaintenanceObjectRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenanceObjectEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanIdV;
import com.zte.uedm.maintenance.manager.domain.shared.enums.MaintenancePlanStatusEnums;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.converter.ObjectConverter;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.po.MaintenanceObjectPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.converter.PlanConverter;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.mapper.MaintenancePlanMapper;
import com.zte.uedm.maintenance.manager.application.command.InspectPlanStatusCommand;
import com.zte.uedm.maintenance.manager.application.query.MaintenancePlanListQuery;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.po.MaintenancePlanPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository.MaintenancePlanRepository;

import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
* <AUTHOR>
*/
@Slf4j
@Repository
public class MaintenancePlanRepositoryImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlanPO>
    implements MaintenancePlanRepository{

    @Resource
    private MaintenancePlanMapper maintenancePlanMapper;
    @Resource
    private MaintenanceObjectRepository maintenanceObjectRepository;


    @Override
    public MaintenancePlanEntity findMaintenancePlanEntity(PlanNameV name, PlanIdV planId) {
        QueryWrapper<MaintenancePlanPO> wrapper = new QueryWrapper<MaintenancePlanPO>()
                .eq("name", name.getName())
                .last(" limit 1");

        if(planId != null){
            wrapper
                .ne("id",planId.getId())
                .apply("base_version_id != (select base_version_id from maintenance_plan where id = {0})",planId.getId());
        }

        log.info("id={},name={}",planId,name.getName());
        MaintenancePlanPO maintenancePlanPo = maintenancePlanMapper.selectOne(wrapper);
        log.info("maintenancePlan{}",maintenancePlanPo);

        //MaintenancePlanPO ---转--> MaintenancePlanEntity
        if(maintenancePlanPo == null){
            return null;
        }

        MaintenancePlanEntity maintenancePlanEntity = new MaintenancePlanEntity();
        BeanUtils.copyProperties(maintenancePlanPo,maintenancePlanEntity);

        return maintenancePlanEntity;
    }

    @Override
    public List<MaintenancePlanEntity> pagePlanByCondition(MaintenancePlanListQuery vo) {
        if (SqlInjectionUtil.hasSqlKeywords(vo.getSort(),vo.getAction())) {
            log.error("there is abnormal word in query");
            return null;
        }
        return maintenancePlanMapper.pagePlanByCondition(vo);
    }

    @Override
    public IPage<MaintenancePlanEntity> pagePlanByCondition(Page<?> page, MaintenancePlanListQuery vo) {
        if (SqlInjectionUtil.hasSqlKeywords(vo.getSort(),vo.getAction())) {
            log.error("there is abnormal word in query");
            return null;
        }
        return maintenancePlanMapper.pagePlanByCondition(page, vo);
    }

    @Override
    public void updateStatusById(InspectPlanStatusCommand inspectPlanStatusDto) {
        maintenancePlanMapper.updateStatusById(inspectPlanStatusDto);
    }

    @Override
    public List<MaintenancePlanPO> selectNeedGenerateTaskPlan(String tomorrowZeroTime, String nextWeekZeroTime, String nextMonthZeroTime) {
        return maintenancePlanMapper.selectNeedGenerateTaskPlan(tomorrowZeroTime,nextWeekZeroTime,nextMonthZeroTime);
    }

    @Override
    public List<MaintenancePlanPO> selectAllEnabledPlan() {
        LambdaQueryWrapper<MaintenancePlanPO> queryWrapper = new LambdaQueryWrapper<MaintenancePlanPO>()
                .eq(MaintenancePlanPO::getStatus, MaintenancePlanStatusEnums.ENABLED.getStatus())
                .apply(" version = (select max(mp.version) from maintenance_plan mp where mp.base_version_id = maintenance_plan.base_version_id)");
        return maintenancePlanMapper.selectList(queryWrapper);
    }

    @Override
    public MaintenancePlanEntity getMaxVersionByBaseVersionId(String baseVersionId) {
        //查询最新计划
        QueryWrapper<MaintenancePlanPO> queryWrapperMaxVersion = new QueryWrapper<MaintenancePlanPO>()
                .eq("base_version_id", baseVersionId)
                .apply("version = (select max(version) from maintenance_plan where base_version_id = {0})", baseVersionId)
                .last(" limit 1");

        return PlanConverter.toEntity(maintenancePlanMapper.selectOne(queryWrapperMaxVersion));
    }
    @Override
    public PlanIdV store(MaintenancePlanEntity planEntity,MaintenancePlanEntity oldPlan) {
        //保存计划
        MaintenancePlanPO maintenancePlanPO = PlanConverter.fromPlanEntity(planEntity);
        log.info("maintenancePlanPO={}",maintenancePlanPO);

        if(null != oldPlan && StringUtils.equals(oldPlan.getId(),planEntity.getId())){
            // 更新 不需要更新维保对象
            maintenancePlanMapper.updateById(maintenancePlanPO);
        }
        else {
            // 新增计划
            maintenancePlanMapper.insert(maintenancePlanPO);

            //新增维保对象
            List<MaintenanceObjectEntity> planObjectEntity = planEntity.getPlanObject();
            planObjectEntity.forEach(obj -> obj.setMaintenancePlanId(maintenancePlanPO.getId()));

            List<MaintenanceObjectPO> objectPOList = ObjectConverter.fromObjectEntityBatch(planObjectEntity);

            log.info("objectPOList={}",maintenancePlanPO);
            maintenanceObjectRepository.saveBatch(objectPOList);
        }

        //保存
        return new PlanIdV(maintenancePlanPO.getId());
    }
    @Override
    public PlanIdV store(MaintenancePlanEntity planEntity) {
        return store(planEntity,null);
    }

    @Override
    public MaintenancePlanEntity getById(PlanIdV planIdV) {
        MaintenancePlanPO maintenancePlanPO = maintenancePlanMapper.selectById(planIdV.getId());

        return PlanConverter.toEntity(maintenancePlanPO);
    }

    @Override
    public void deletePlan(List<String> planIdList) {
        //先删除计划
        maintenancePlanMapper.deleteBatchIds(planIdList);

        //再删除计划关联的对象
        maintenanceObjectRepository.removeByPlanIds(planIdList);
    }

    @Override
    public List<MaintenancePlanEntity> findHistoryVersionPlan(PlanIdV planId) {
        //查询 所有历史版本
        QueryWrapper<MaintenancePlanPO> planQueryWrapper = new QueryWrapper<MaintenancePlanPO>()
                .apply("base_version_id = (select base_version_id from maintenance_plan where id = {0})", planId.getId());

        List<MaintenancePlanPO> planList = maintenancePlanMapper.selectList(planQueryWrapper);

        log.info("findHistoryVersionPlan planId={},result={}",planId,planList.size());

        return PlanConverter.toEntityBatch(planList);
    }


    @Override
    public List<MaintenancePlanPO> selectHistoryVersion(String baseVersionId, String id)
    {
        return maintenancePlanMapper.selectList(new QueryWrapper<MaintenancePlanPO>()
                .eq("base_version_id", baseVersionId)
                .ne("id", id)
                .orderByAsc("version"));
    }
}




