package com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 维保对象表
 * @TableName maintenance_object
 */
@TableName(value ="maintenance_object")
@Getter
@Setter
@ToString
public class MaintenanceObjectPO implements Serializable {
    /**
     * 维保对象表id
     */
    @TableId
    private String id;

    /**
     * 维保计划id
     */
    private String maintenancePlanId;

    /**
     * 维保对象实例id
     */
    private String instanceId;

    /**
     * 维保对象实例名称
     */
    private String instanceName;

    /**
     * 维保对象类型
     */
    private String type;

    /**
     * 维保对象资源类型，区分监控设备监控对象
     */
    private String resourceType;

    /**
     * 维保对象选择的维保模板 json
     */
    private String template;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}