package com.zte.uedm.maintenance.manager.infrastructure.repository.maintenancetask.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MaintenanceTaskListPO {
    /**
     * 维保任务表id
     */
    private String id;

    /**
     * 维保计划id
     */
    private String maintenancePlanId;

    /**
     * 维保任务名称
     */
    private String name;

    /**
     * 维保任务状态 1待执行 2执行中 3已完成 4超期待执行 5超期完成 6超期未完成
     */
    private Integer status;


    private Integer conclusion;

    /**
     * 任务是否下载 true 已完成 false未下载
     */
    private Boolean download;

    /**
     * 责任人
     */
    private String responsiblePerson;


    /**
     * 执行部门
     */
    private String executeDepartment;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 实际执行人
     */
    private String taskExecutor;

    /**
     * 维保任务创建时间
     */
    private String taskCreateTime;

    /**
     * 维保任务开始时间
     */
    private String taskBeginTime;

    /**
     * 维保任务结束时间
     */
    private String taskEndTime;

    /**
     * 类型
     */
    private String maintenanceTaskType;

}
