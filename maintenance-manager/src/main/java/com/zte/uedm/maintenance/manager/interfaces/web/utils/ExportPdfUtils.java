package com.zte.uedm.maintenance.manager.interfaces.web.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.AttachmentBean;
import com.zte.uedm.maintenance.manager.interfaces.web.dto.MaintenanceTaskDetailDto;
import com.zte.uedm.maintenance.manager.interfaces.web.rpc.impl.ConfigurationManagerRpcImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class ExportPdfUtils
{
    private static final String UTF_8 = "UTF-8";
    private static final String ZH_CN = "zh-CN";
    private static final String ZH_TITLE = "维保报告";
    private static final String EN_TITLE = "Maintenance Report";
    private static final String ZH_SUBTITLE_1 = "计划基本信息";
    private static final String EN_SUBTITLE_1 = "Program Basic Information";
    private static final String ZH_SUBTITLE_2 = "维保任务基本信息";
    private static final String EN_SUBTITLE_2 = "Task Basic Information";
    private static final String ZH_SUBTITLE_3 = "维保对象信息";
    private static final String EN_SUBTITLE_3 = "Maintenance Object";
    private static final String ZH_ABNORMAL = " 异常数量:";
    private static final String EN_ABNORMAL = " ABNORMAL:";
    private static final String ZH_NONE = " 无";
    private static final String EN_NONE = " NONE";
    private static final List<String> ZH_SUBTITLE_DESC_1 = Arrays.asList("计划名称", "责任部门", "责任人", "执行部门", "执行人", "计划起止时间",
            "提醒方式", "版本", "类型");
    private static final List<String> EN_SUBTITLE_DESC_1 = Arrays.asList("Plan Name", "Responsible Department",
            "Responsible Person", "Executive Department", "Executor", "Plan Time", "Notice Way", "Version", "Type");
    private static final List<String> ZH_SUBTITLE_DESC_2 = Arrays.asList("任务名称", "状态", "结论", "任务起止时间", "维保对象数", "维保项总数",
            "正常项", "异常项", "不涉及项");
    private static final List<String> EN_SUBTITLE_DESC_2 = Arrays.asList("Task Name", "Status", "Conclusion",
            "Task Time", "Object Total", "Items Total", "Normal Items", "Abnormal Items", "Not Involved");
    private static final List<String> ZH_SUBTITLE_DESC_3 = Arrays.asList("维保内容", "维保附件", "结论", "附件", "异常内容");
    private static final List<String> EN_SUBTITLE_DESC_3 = Arrays.asList("Maintenance Content", "Attachment",
            "Conclusion", "Attachment", "Abnormal Content");
    private static final String CONTENT_DISPOSITION = "Content-Disposition";
    private static final float DEFAULT_FIRSTPAGE_HEIGHT = 50.0F;//the distance to the firstpageUpper
    private static final float DEFAULT_NEWPAGE_HIGHT = 16.0F;//the distance to the firstpageUpper
    private static final int DEFAULT_TITLE_FONTSIZE = 14;
    private static final int DEFAULT_SUBTITLE_FONTSIZE = 12;
    private static final int DEFAULT_VALUE_FONTSIZE = 10;
    private static final int MARGIN_VALUE = 12;/*默认间隔*/
    private static final float PAGE_HEIGHT = PDRectangle.A4.getHeight();
    private static final float PAGE_WIDTH = PDRectangle.A4.getWidth();
    private static final float LEFT_VALUE = DEFAULT_VALUE_FONTSIZE * 6;/*左对齐位置*/
    private static final float RIGHT_VALUE = (PAGE_WIDTH - 2 * MARGIN_VALUE) / 2 + LEFT_VALUE;/*右对齐位置*/
    private static final float IMAGE_WIDTH = 200;
    private static final float IMAGE_HEIGHT = 140;
    private static final List<String> IMAGE_TYPE = Arrays.asList("jpg", "png", "jpeg", "bmp");
    private static final String DEFAULT_VALUE = "--";
    private PDFont font = null;
    private static final int START_X = 40;
    private static final int TAB_X = 60;
    private PDDocument doc = null;
    private PDPageContentStream contents = null;
    private float currHeight = 0f; /*当前高度*/
    private float currWidth = 0f; /*当前X位置*/
    private PDPage page = null;
    private int pageNum = 0;

    @Resource
    private ConfigurationManagerRpcImpl configurationManagerRpc;

    public void init() throws Exception
    {
        try
        {
            this.doc = new PDDocument();
            if (this.getClass() != null)
            {
                if (this.getClass().getClassLoader() != null)
                {
                    if (this.getClass().getClassLoader().getResource("simhei.ttf") != null)
                    {
                        String pathL = this.getClass().getClassLoader().getResource("simhei.ttf").getFile();
                        if (pathL != null)
                        {
                            font = PDType0Font.load(this.doc, new File(pathL));
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error("pdf字体初始化异常" + e);
            throw new IOException(e);
        }
    }

    public void exportPdf(MaintenanceTaskDetailDto reportVo, HttpServletResponse response, String language)
            throws Exception
    {
        init();
        ServletOutputStream out = null;
        try
        {
            /*创建第一页pdf*/
            PDRectangle defaultPage = new PDRectangle(PAGE_WIDTH, PAGE_HEIGHT);
            page = new PDPage(defaultPage);
            pageNum = 1;
            doc.addPage(page);
            contents = new PDPageContentStream(doc, page);
            /*标题*/
            currWidth = START_X;
            currHeight = page.getMediaBox().getUpperRightY() - DEFAULT_FIRSTPAGE_HEIGHT - MARGIN_VALUE;
            contents.beginText();
            contents.setFont(font, DEFAULT_TITLE_FONTSIZE);
            contents.newLineAtOffset(PAGE_WIDTH / 2 -
                    (float) getMarginNum(getValueByLanguage(language, ZH_TITLE, EN_TITLE), language)
                            * DEFAULT_TITLE_FONTSIZE / 2, currHeight);
            String valueByLanguage = getValueByLanguage(language, ZH_TITLE, EN_TITLE);
            contents.showText(valueByLanguage);
            contents.endText();

            /*计划基本信息*/
            currHeight = currHeight - DEFAULT_FIRSTPAGE_HEIGHT;
            writeContent(font, DEFAULT_SUBTITLE_FONTSIZE, getValueByLanguage(language, ZH_SUBTITLE_1, EN_SUBTITLE_1),
                    Color.black);
            currHeight = currHeight - 6;
            drawLineGraphic();

            /*第一段落信息*/
            getSubDesc_1Data(reportVo, language);

            /*处理其他信息*/
            dealData(reportVo, language);

            contents.close();
            response.reset();
            response.setCharacterEncoding(UTF_8);
            response.setContentType("application/pdf;charset=UTF-8");
            String fileName = reportVo.getTaskInfo().getName() + ".pdf";
            fileName = URLEncoder.encode(fileName, UTF_8);
            response.setHeader(CONTENT_DISPOSITION, "attachment;filename=".concat(fileName));
            out = response.getOutputStream();
            doc.save(out);

        }
        catch (Exception e)
        {
            throw e;
        }
        finally
        {
            doc.close();
            if (out != null)
            {
                out.close();
            }
        }
    }

    public void dealData(MaintenanceTaskDetailDto reportVo, String language) throws IOException, UedmException
    {
        /*维保任务基本信息*/
        currWidth = START_X;
        currHeight = currHeight - 2 * DEFAULT_NEWPAGE_HIGHT;
        writeContent(font, DEFAULT_SUBTITLE_FONTSIZE, getValueByLanguage(language, ZH_SUBTITLE_2, EN_SUBTITLE_2),
                Color.black);
        currHeight = currHeight - 6;

        drawLineGraphic();
        /*任务名称*/
        currHeight = currHeight - DEFAULT_TITLE_FONTSIZE;
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(0), EN_SUBTITLE_DESC_2.get(0)) + " "
                        + reportVo.getTaskInfo().getName(), Color.black);
        /*状态*/
        currWidth = START_X;
        currHeight = currHeight - DEFAULT_TITLE_FONTSIZE;
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(1), EN_SUBTITLE_DESC_2.get(1)) + " "
                        + ExportExcelUtils.getStatus(reportVo.getTaskInfo().getStatus(), language), Color.black);
        /*结论*/
        currWidth = RIGHT_VALUE;
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(2), EN_SUBTITLE_DESC_2.get(2)) + " "
                        + ExportExcelUtils.getConclusion(reportVo.getTaskInfo().getConclusion(), language),
                Color.black);
        /*任务起止时间*/
        currWidth = START_X;
        currHeight = currHeight - DEFAULT_TITLE_FONTSIZE;
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(3), EN_SUBTITLE_DESC_2.get(3)) + " "
                        + ExportExcelUtils.getFormatTime(reportVo.getTaskInfo().getTaskBeginTime()) + " - "
                        + ExportExcelUtils.getFormatTime(reportVo.getTaskInfo().getTaskEndTime()), Color.black);

        /*维保对象数*/
        currHeight = currHeight - 2 * DEFAULT_NEWPAGE_HIGHT;

        currWidth = TAB_X;
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(4), EN_SUBTITLE_DESC_2.get(4)), Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * getMarginNum(
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(4), EN_SUBTITLE_DESC_2.get(4)), language);
        writeContent(font, DEFAULT_VALUE_FONTSIZE, reportVo.getTaskInfo().getMaintenanceObjectCount() + "",
                Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * 6;
        /*维保项总数*/
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(5), EN_SUBTITLE_DESC_2.get(5)), Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * getMarginNum(
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(5), EN_SUBTITLE_DESC_2.get(5)), language);
        writeContent(font, DEFAULT_VALUE_FONTSIZE, reportVo.getTaskInfo().getMaintenanceItemCount() + "", Color.black);

        currWidth = TAB_X;
        currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
        Integer normalItemCount = reportVo.getTaskInfo().getNormalItemCount();
        Integer maintenanceItemCount = reportVo.getTaskInfo().getMaintenanceItemCount();
        Integer abnormalItemCount = reportVo.getTaskInfo().getAbnormalItemCount();
        BigDecimal normal = new BigDecimal((double) normalItemCount / (double) maintenanceItemCount * 100).setScale(2,
                RoundingMode.HALF_UP);
        BigDecimal abnormal = new BigDecimal((double) abnormalItemCount / (double) maintenanceItemCount * 100).setScale(
                2, RoundingMode.HALF_UP);

        //异常项/异常率/无涉及百分比进度条
        drawGraphic(normal.floatValue(), abnormal.floatValue());

        currWidth = TAB_X;
        currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT - 5;
        /*正常项/正常率*/
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(6), EN_SUBTITLE_DESC_2.get(6)), Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * getMarginNum(
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(6), EN_SUBTITLE_DESC_2.get(6)), language);
        writeContent(font, DEFAULT_VALUE_FONTSIZE, reportVo.getTaskInfo().getNormalItemCount() + "/" + normal + "%",
                Color.GREEN);

        currWidth = currWidth + 8 * DEFAULT_VALUE_FONTSIZE;
        /*异常项/异常率*/
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(7), EN_SUBTITLE_DESC_2.get(7)), Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * getMarginNum(
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(7), EN_SUBTITLE_DESC_2.get(7)), language);
        /* Started by AICoder, pid:c384dc40d38d322143870b1a5027440a4fd18e9b */
        writeContent(font, DEFAULT_VALUE_FONTSIZE, reportVo.getTaskInfo().getAbnormalItemCount() + "/" + abnormal + "%", Color.RED);
        /* Ended by AICoder, pid:c384dc40d38d322143870b1a5027440a4fd18e9b */
        currWidth = RIGHT_VALUE + 3 * DEFAULT_VALUE_FONTSIZE;
        /*不涉及项/不涉及率*/
        writeContent(font, DEFAULT_VALUE_FONTSIZE,
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(8), EN_SUBTITLE_DESC_2.get(8)), Color.black);
        currWidth = currWidth + DEFAULT_VALUE_FONTSIZE * getMarginNum(
                getValueByLanguage(language, ZH_SUBTITLE_DESC_2.get(8), EN_SUBTITLE_DESC_2.get(8)), language);
        writeContent(font, DEFAULT_VALUE_FONTSIZE, reportVo.getTaskInfo().getNotInvolvedCount() + "/" + new BigDecimal(
                (double) reportVo.getTaskInfo().getNotInvolvedCount() / (double) reportVo.getTaskInfo()
                        .getMaintenanceItemCount() * 100).setScale(2, RoundingMode.HALF_UP) + "%", Color.GRAY);

        currWidth = START_X;
        /*维保对象信息*/
        currHeight = currHeight - 2 * DEFAULT_NEWPAGE_HIGHT;
        writeContent(font, DEFAULT_SUBTITLE_FONTSIZE, getValueByLanguage(language, ZH_SUBTITLE_3, EN_SUBTITLE_3),
                Color.black);
        currHeight = currHeight - 5;
        drawLineGraphic();
        /*详情*/
        writeDetail(reportVo, language);
    }

    public void getSubDesc_1Data(MaintenanceTaskDetailDto reportVo, String language) throws IOException
    {
        /*构造第一段落数据*/
        List<String> desc_1 = new ArrayList<>();
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getName()));
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getResponsibleDepartment()));
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getResponsiblePerson()));
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getExecuteDepartment()));
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getExecutor()));
        desc_1.add(ExportExcelUtils.getFormatTime(reportVo.getPlanInfo().getPlanBeginTime()) + " - "
                + ExportExcelUtils.getFormatTime(reportVo.getPlanInfo().getPlanEndTime()));
        desc_1.add(setDefaultValue(ExportExcelUtils.getRemindType(reportVo.getPlanInfo().getReminderMode(), language)));
        desc_1.add(setDefaultValue(reportVo.getPlanInfo().getVersion()));
        desc_1.add(setDefaultValue(
                ExportExcelUtils.getPlanType(reportVo.getPlanInfo().getMaintenancePlanType(), language)));
        log.info("--begin deal desc-1--");
        /*处理第一段落数据*/
        dealSubDesc_1(desc_1, language);
        log.info("--end deal desc-1--");
    }

    public String setDefaultValue(String value)
    {
        if (StringUtils.isBlank(value))
        {
            value = DEFAULT_VALUE;
        }
        return value;
    }

    /*处理第一段落内容*/
    public void dealSubDesc_1(List<String> dataList, String language) throws IOException
    {
        if (StringUtils.isBlank(language))
        {
            language = ZH_CN;
        }
        List<String> enSubtitleDesc1 = ZH_CN.equals(language) ? ZH_SUBTITLE_DESC_1 : EN_SUBTITLE_DESC_1;
        List<String> desc = new ArrayList<>();
        for (int i = 0; i < enSubtitleDesc1.size(); i++)
        {
            desc.add(enSubtitleDesc1.get(i) + " " + dataList.get(i));
        }
        for (int j = 0; j < desc.size(); j++)
        {
            if (j == 0)
            {
                /*计划名称单独一行*/
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                currWidth = START_X;
                writeContent(font, DEFAULT_VALUE_FONTSIZE, desc.get(j), Color.black);
            }
            else if (j % 2 == 0)
            {
                /*奇数在右边，不需要换行*/
                currWidth = RIGHT_VALUE;
                writeContent(font, DEFAULT_VALUE_FONTSIZE, desc.get(j), Color.black);
            }
            else
            {
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                currWidth = START_X;
                writeContent(font, DEFAULT_VALUE_FONTSIZE, desc.get(j), Color.black);
            }
        }
    }

    public int getMarginNum(String value, String language)
    {
        if (StringUtils.isBlank(value))
        {
            return 0;
        }
        if (ZH_CN.equals(language))
        {
            return value.length() + 1;
        }
        return value.length() / 2 + 1;
    }

    public String getValueByLanguage(String language, String zh_value, String en_value)
    {
        if (ZH_CN.equals(language))
        {
            return zh_value;
        }
        return en_value;
    }

    public void writeDetail(MaintenanceTaskDetailDto reportVo, String language) throws IOException, UedmException
    {
        /*循环渲染路线及巡检点巡检对象信息*/
        List<MaintenanceTaskDetailDto.MaintenanceObjectInfo> details = reportVo.getMaintenanceObjectInfos();

        for (MaintenanceTaskDetailDto.MaintenanceObjectInfo objectInfo : details)
        {
            List<MaintenanceTaskDetailDto.ItemResult> itemResults = objectInfo.getItemResults();
            for (MaintenanceTaskDetailDto.ItemResult result : itemResults)
            {
                currWidth = START_X;
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                /*维保对象和异常数量*/
                writeContent(font, DEFAULT_VALUE_FONTSIZE + 1, objectInfo.getInstanceName(), Color.black);
                currWidth = currWidth
                        + ExportExcelUtils.getStrLength(objectInfo.getInstanceName()) * DEFAULT_VALUE_FONTSIZE;
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_ABNORMAL, EN_ABNORMAL) + objectInfo.countAbnormalNum(),
                        Color.red);
                currWidth = START_X;
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                /*维保项*/
                writeContent(font, DEFAULT_VALUE_FONTSIZE, result.getName(), Color.black);
                currWidth = START_X;
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                /*维保内容*/
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_SUBTITLE_DESC_3.get(0), EN_SUBTITLE_DESC_3.get(0)) + " "
                                + result.getContent(), Color.black);
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                String itemAttachment = isImage(result.getItemAttachment(), 1) ?
                        "" :
                        getValueByLanguage(language, ZH_NONE, EN_NONE);
                itemAttachment = showText(showFileName(result.getItemAttachment(), 1), itemAttachment);
                /*维保附件*/
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_SUBTITLE_DESC_3.get(1), EN_SUBTITLE_DESC_3.get(1)) + " "
                                + itemAttachment, Color.black);
                /*附件展示*/
                dealAttachment(result.getItemAttachment(), 1);

                /*结论*/
                currWidth = START_X;
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_SUBTITLE_DESC_3.get(2), EN_SUBTITLE_DESC_3.get(2)) + " "
                                + ExportExcelUtils.getConclusion(result.getConclusion(), language), Color.black);
                /*附件*/
                currWidth = RIGHT_VALUE;
                String attachment = isImage(result.getResultAttachment(), 2) ?
                        "" :
                        getValueByLanguage(language, ZH_NONE, EN_NONE);
                /*获取附件展示文字*/
                attachment = showText(showFileName(result.getResultAttachment(), 2), attachment);
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_SUBTITLE_DESC_3.get(3), EN_SUBTITLE_DESC_3.get(3)) + " "
                                + attachment, Color.black);
                /*附件展示*/
                dealAttachment(result.getResultAttachment(), 2);
                /*异常情况*/
                currWidth = START_X;
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
                writeContent(font, DEFAULT_VALUE_FONTSIZE,
                        getValueByLanguage(language, ZH_SUBTITLE_DESC_3.get(4), EN_SUBTITLE_DESC_3.get(4)) + " " + (
                                StringUtils.isBlank(result.getAbnormalContent()) ?
                                        getValueByLanguage(language, ZH_NONE, EN_NONE) :
                                        result.getAbnormalContent()), Color.black);
                currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
            }
        }

    }

    /*解析维保附件字符串,单个文件*/
    public AttachmentBean getItemAttachment(String value)
    {
        AttachmentBean attachmentBean = new AttachmentBean();
        if (StringUtils.isNotBlank(value))
        {
            attachmentBean = JSON.parseObject(value, AttachmentBean.class);
        }
        return attachmentBean;
    }

    /*解析附件字符串,多个文件*/
    public List<AttachmentBean> getAttachments(String value)
    {
        List<AttachmentBean> list = new ArrayList<>();
        if (StringUtils.isNotBlank(value))
        {
            list = JSONObject.parseArray(value, AttachmentBean.class);
        }
        return list;
    }

    /*处理附件展示*/
    public void dealAttachment(String attachment, int type) throws IOException, UedmException
    {
        //附件如果是图片需要展示
        currWidth = START_X + 4 * DEFAULT_VALUE_FONTSIZE;
        if (isImage(attachment, type))
        {
            currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT - IMAGE_HEIGHT;
        }
        if (type == 1)
        {
            AttachmentBean itemAttachment = getItemAttachment(attachment);
            if (isImage(itemAttachment))
            {
                String base64Str = configurationManagerRpc.picturePreviewByFileId(itemAttachment.getId());
                PDImageXObject img = dealImageValue(base64Str, itemAttachment.getFileSuffix(), doc);
                if (img != null)
                {
                    writeImage(img);
                }
            }
        }
        else
        {
            /*结果附件展示*/
            dealResultAttachment(attachment);
        }
    }

    public void dealResultAttachment(String attachment) throws UedmException, IOException
    {
        List<AttachmentBean> list = getAttachments(attachment);
        List<String> fileIds = new ArrayList<>();
        List<String> suffix = new ArrayList<>();
        if(null!=list) {
            for (AttachmentBean attachmentBean : list) {
                if (isImage(attachmentBean)) {
                    fileIds.add(attachmentBean.getId());
                    suffix.add(attachmentBean.getFileSuffix());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(fileIds))
        {
            List<String> base64List = configurationManagerRpc.picturePreviewByFileIds(fileIds);
            for (int j = 0; j < base64List.size(); j++)
            {
                PDImageXObject img = dealImageValue(base64List.get(j), suffix.get(j), doc);
                if (img != null)
                {
                    /*从左向右写入图片，超过宽度就换行*/
                    if (j > 0)
                    {
                        /*不是首张图片*/
                        currWidth = currWidth + IMAGE_WIDTH;
                    }
                    writeImage(img);
                }
            }
        }
    }

    public boolean isImage(String attachment, int type)
    {
        boolean flag = false;
        if (StringUtils.isNotBlank(attachment))
        {
            if (type == 1)
            {
                AttachmentBean itemAttachment = getItemAttachment(attachment);
                flag = isImage(itemAttachment);
            }
            else
            {
                List<AttachmentBean> list = getAttachments(attachment);
                if(null!=list) {
                    for (AttachmentBean attachmentBean : list) {
                        flag = isImage(attachmentBean);
                        if (flag) {
                            break;
                        }
                    }
                }
            }
        }
        return flag;
    }

    public Boolean isImage(AttachmentBean itemAttachment)
    {
        boolean flag = false;
        if (IMAGE_TYPE.contains(itemAttachment.getFileSuffix()) && StringUtils.isNotBlank(itemAttachment.getId()))
        {
            flag = true;
        }
        return flag;
    }

    public String showFileName(String attachment, int type)
    {
        String result = "";
        if (StringUtils.isNotBlank(attachment))
        {
            if (type == 1)
            {
                AttachmentBean itemAttachment = getItemAttachment(attachment);
                if (!isImage(itemAttachment))
                {
                    result = itemAttachment.getName();
                }
            }
            else
            {
                List<AttachmentBean> list = getAttachments(attachment);
                List<String> fileList = new ArrayList<>();
                if(null!=list) {
                    for (AttachmentBean attachmentBean : list) {
                        if (!isImage(attachmentBean)) {
                            fileList.add(attachmentBean.getName());
                        }
                    }
                }
                result = StringUtils.join(fileList, ",");
            }
        }

        return result;
    }

    public String showText(String fileName, String isImage)
    {
        String result = "";
        if (StringUtils.isNotBlank(fileName))
        {
            /*如果有非图片附件，展示文件名*/
            result = fileName;
        }
        else
        {
            result = isImage;
        }
        return result;
    }

    /*写入文本内容*/
    private void writeContent(PDFont font, int fontSize, String value, Color color)
    {
        if (StringUtils.isBlank(value))
        {
            value = DEFAULT_VALUE;
        }
        value = value.replaceAll("\r|\n|\t", "");
        try
        {
            float length = (float) (ExportExcelUtils.getStrLength(value) * DEFAULT_VALUE_FONTSIZE) / 2;
            if (length > PAGE_WIDTH - 2 * MARGIN_VALUE)
            {
                writeLongText(value);
            }
            else
            {
                checkWidthAndPage();
                contents.beginText();
                contents.setFont(font, fontSize);
                contents.setNonStrokingColor(color);
                contents.newLineAtOffset(currWidth, currHeight);
                contents.showText(value);
                contents.endText();
            }
        }
        catch (IOException e)
        {
            log.error("writeContent error:", e);
        }
    }

    /*如果某一行太长则按每行最多字符输入，过长换行*/
    public void writeLongText(String text) throws IOException
    {
        List<String> lines = new ArrayList<String>();
        int key = 0;
        StringBuilder sb = new StringBuilder();
        int length = text.length();
        int lineNum = 0;

        while (key + 1 <= length)
        {
            sb.append(text.substring(key, key + 1));
            key++;
            float size = (float) DEFAULT_VALUE_FONTSIZE * ExportExcelUtils.getStrLength(sb.toString()) / 2;
            size = lineNum == 0 ? size + currWidth : size + 2 * START_X;
            if ((size) >= (PAGE_WIDTH - START_X) || ((key) == text.length()))
            {
                lineNum++;
                lines.add(sb.toString());
                sb.delete(0, sb.length());
            }
        }
        for (String line : lines)
        {
            writeContent(font, DEFAULT_VALUE_FONTSIZE, line, Color.black);
            currWidth = 2 * START_X;
            currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
        }
        currWidth = START_X;
    }

    public void checkWidthAndPage() throws IOException
    {
        if (currWidth > PAGE_WIDTH - 2 * MARGIN_VALUE)
        {
            /*超过一行则换行*/
            currWidth = START_X + 2 * DEFAULT_VALUE_FONTSIZE;
            currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT;
        }
        if (currHeight - MARGIN_VALUE < 0)
        {
            /*超过页的高度，新建一页，并重置当前高度,统计页数*/
            PDRectangle defaultPage = new PDRectangle(PAGE_WIDTH, PAGE_HEIGHT);
            page = new PDPage(defaultPage);
            doc.addPage(page);
            contents.close();
            contents = new PDPageContentStream(doc, page);
            currHeight = page.getMediaBox().getUpperRightY() - MARGIN_VALUE - DEFAULT_TITLE_FONTSIZE;
            pageNum += 1;
            log.info("pageNum ==> {}", pageNum);
        }
    }

    /*写入图片内容*/
    public void writeImage(PDImageXObject img) throws IOException
    {
        checkImagePosition();
        contents.drawImage(img, currWidth, currHeight, IMAGE_WIDTH, IMAGE_HEIGHT);
        currWidth = currWidth + MARGIN_VALUE;
    }

    public void checkImagePosition() throws IOException
    {
        if ((currWidth + IMAGE_WIDTH) > (PAGE_WIDTH - 2 * MARGIN_VALUE))
        {
            /*超过一行则换行*/
            currWidth = START_X + 3 * DEFAULT_VALUE_FONTSIZE;
            currHeight = currHeight - DEFAULT_NEWPAGE_HIGHT - IMAGE_HEIGHT;
        }
        if (currHeight - MARGIN_VALUE < 0)
        {
            /*超过页的高度，新建一页，并重置当前高度,统计页数*/
            PDRectangle defaultPage = new PDRectangle(PAGE_WIDTH, PAGE_HEIGHT);
            page = new PDPage(defaultPage);
            doc.addPage(page);
            contents.close();
            contents = new PDPageContentStream(doc, page);
            currHeight = page.getMediaBox().getUpperRightY() - MARGIN_VALUE - IMAGE_HEIGHT;
            pageNum += 1;
            log.info("pageNum ==> {}", pageNum);
        }
    }

    /**
     * 处理巡检项Base64Str
     *
     * @param value
     * @param suffix
     * @param doc
     * @return
     * @throws IOException
     */
    public PDImageXObject dealImageValue(String value, String suffix, PDDocument doc) throws IOException
    {
        PDImageXObject pdImage = null;
        if (StringUtils.isBlank(value))
        {
            return null;
        }
        /*暂时只支持jpg/png图片预览*/
        pdImage = PDImageXObject.createFromByteArray(doc, Base64Convert.generateImage(value), "file" + "." + suffix);
        return pdImage;
    }

    /*画出百分比线条*/
    public void drawGraphic(float rightRate, float errorRate)
    {
        try
        {
            float notInvolveRate = 100 - rightRate - errorRate;
            float width_1 = (PAGE_WIDTH - MARGIN_VALUE * 2 - 3 * START_X) * rightRate / 100;
            float width_2 = (PAGE_WIDTH - MARGIN_VALUE * 2 - 3 * START_X) * errorRate / 100;
            float width_3 = (PAGE_WIDTH - MARGIN_VALUE * 2 - 3 * START_X) * notInvolveRate / 100;
            contents.setNonStrokingColor(Color.GREEN);
            /*绘制正确的绿线*/
            contents.addRect(currWidth, currHeight, width_1, 2f);
            contents.fill();
            contents.setNonStrokingColor(Color.RED);
            /*绘制错误的红线*/
            contents.addRect(currWidth + width_1, currHeight, width_2, 2f);
            contents.fill();
            contents.setNonStrokingColor(Color.GRAY);
            /*绘制不涉及的黄线*/
            contents.addRect(currWidth + width_1 + width_2, currHeight, width_3, 2f);
            contents.fill();
            contents.setNonStrokingColor(Color.BLACK);
            log.info("绘制正确率/异常率/不涉及线条结束！");
        }
        catch (IOException e)
        {
            log.error("绘制正确率/异常率/不涉及线条失败！", e);
        }
    }

    /*画出标题下划线*/
    public void drawLineGraphic()
    {
        try
        {
            float width_1 = (float) ((PAGE_WIDTH - START_X * 2) * 0.2);
            float width_2 = (float) ((PAGE_WIDTH - START_X * 2) * 0.8);
            contents.setNonStrokingColor(Color.blue);
            /*蓝线*/
            contents.addRect(currWidth, currHeight, width_1, 1.5f);
            contents.fill();

            contents.setNonStrokingColor(Color.gray);
            /*绘制灰线*/
            contents.addRect(currWidth + width_1, currHeight, width_2, 1.5f);
            contents.fill();
            contents.setNonStrokingColor(Color.black);
        }
        catch (IOException e)
        {
            log.error("绘制正确率/异常率线条失败！", e);
        }
    }
}
