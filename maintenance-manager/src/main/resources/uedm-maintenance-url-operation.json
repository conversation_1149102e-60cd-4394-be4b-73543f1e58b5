[{"url": "/api/maintenance-manager/v1/swagger", "method": "GET", "operation": "operation.swagger"}, {"url": "/api/maintenance-manager/v1/swagger.json", "method": "GET", "operation": "operation.swagger"}, {"url": "/api/maintenance-manager/v1/swagger.yaml", "method": "GET", "operation": "operation.swagger"}, {"url": "/iui/{respath:.+}", "method": "GET", "operation": "global.operation"}, {"url": "/iui/{respath:.+}", "method": "POST", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "POST", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "GET", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "DELETE", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "PUT", "operation": "global.operation"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/query", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/detail-by-id", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/name-check", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/inspect-group/page-user-condition", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/tree/get-child-template", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/monitor-device/get-by-condition-module", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/select-by-obj", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.query"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/copy-edit-detail", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.plan.mod:operation.uedm.maintenance.f.maintenance.plan.copy"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/edit", "method": "PUT", "operation": "operation.uedm.maintenance.f.maintenance.plan.mod"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/add", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.add"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.f.maintenance.plan.delete"}, {"url": "/api/maintenance-manager/v1/maintenance-plan/copy", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.plan.copy"}, {"url": "/api/maintenance-manager/v1/maintenance-template/search-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.template.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/download-default-template-excel", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.template.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/add-edit-copy", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.template.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/name-check", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.template.query"}, {"url": "/api/maintenance-manager/v1/maintenance-template/delete-by-id", "method": "DELETE", "operation": "operation.uedm.maintenance.f.maintenance.template.delete"}, {"url": "/api/maintenance-manager/v1/maintenance-template/upload-template-by-excel", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.template.import"}, {"url": "/api/maintenance-manager/v1/maintenance-task/query", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.task.query:operation.uedm.maintenance.f.maintenance.task.query.all"}, {"url": "/api/maintenance-manager/v1/maintenance-task/detail-by-id", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.task.query:operation.uedm.maintenance.f.maintenance.task.query.all:operation.uedm.maintenance.f.maintenance.task.convert.risk"}, {"url": "/api/maintenance-manager/v1/malfunction/existTask", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.task.convert.malfunction:operation.uedm.maintenance.f.inspect.task.convert.malfunction"}, {"url": "/api/maintenance-manager/v1/risk/existTask", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.task.convert.risk"}, {"url": "/api/maintenance-manager/v1/maintenance-task/preview", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.task.report.preview"}, {"url": "/api/maintenance-manager/v1/maintenance-task/download", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.task.report.download"}, {"url": "/api/maintenance-manager/v1/maintenance-task/queryMaintenanceCalendar", "method": "GET", "operation": "operation.uedm.maintenance.f.maintenance.calendar"}, {"url": "/api/maintenance-manager/v1/maintenance-task/taskCalendarQuery", "method": "POST", "operation": "operation.uedm.maintenance.f.maintenance.calendar"}, {"url": "/api/maintenance-manager/v1/risk/getUsersByCondition", "method": "POST", "operation": "operation.uedm.maintenance.f.inspect.plan.query:operation.uedm.maintenance.f.risk.initiated.query:operation.uedm.maintenance.f.risk.toBeHandled.query:operation.uedm.maintenance.f.risk.allTicket.query"}, {"url": "/api/maintenance-manager/v1/malfunction/queryListCreated", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query"}, {"url": "/api/maintenance-manager/v1/malfunction/reminder", "method": "GET", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query:operation.uedm.maintenance.f.malfunction.allTicket.query"}, {"url": "/api/maintenance-manager/v1/malfunction/deal-flow", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query:operation.uedm.maintenance.f.malfunction.toBeHandled.query:operation.uedm.maintenance.f.malfunction.allTicket.query"}, {"url": "/api/maintenance-manager/v1/malfunction/queryDetails", "method": "GET", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query:operation.uedm.maintenance.f.malfunction.toBeHandled.query:operation.uedm.maintenance.f.malfunction.handled.query:operation.uedm.maintenance.f.malfunction.allTicket.query"}, {"url": "/api/maintenance-manager/v1/malfunction/name-check", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query"}, {"url": "/api/maintenance-manager/v1/malfunction/getUsersByCondition", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query:operation.uedm.maintenance.f.malfunction.toBeHandled.query:operation.uedm.maintenance.f.malfunction.allTicket.query:operation.uedm.config.forward.b.alarmToMalfunction.add:operation.uedm.config.forward.b.alarmToMalfunction.mod"}, {"url": "/api/maintenance-manager/v1/malfunction/temporary", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query"}, {"url": "/api/maintenance-manager/v1/malfunction/add", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.query"}, {"url": "/api/maintenance-manager/v1/malfunction/delete-by-id", "method": "DELETE", "operation": "operation.uedm.maintenance.f.malfunction.initiated.delete"}, {"url": "/api/maintenance-manager/v1/malfunction/exportListCreated", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.initiated.export"}, {"url": "/api/maintenance-manager/v1/malfunction/queryListPending", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.toBeHandled.query"}, {"url": "/api/maintenance-manager/v1/malfunction/exportListPending", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.toBeHandled.export"}, {"url": "/api/maintenance-manager/v1/malfunction/queryListHandled", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.handled.query"}, {"url": "/api/maintenance-manager/v1/malfunction/exportListHandled", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.handled.export"}, {"url": "/api/maintenance-manager/v1/malfunction/queryListAll", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.allTicket.query"}, {"url": "/api/maintenance-manager/v1/malfunction/exportListAll", "method": "POST", "operation": "operation.uedm.maintenance.f.malfunction.allTicket.export"}, {"url": "/api/maintenance-manager/v1/risk/get_risk_form", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.report.query"}, {"url": "/api/maintenance-manager/v1/risk/webOutput", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.report.output"}, {"url": "/api/maintenance-manager/v1/risk/queryListCreated", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.query"}, {"url": "/api/maintenance-manager/v1/risk/reminder", "method": "GET", "operation": "operation.uedm.maintenance.f.risk.initiated.query:operation.uedm.maintenance.f.risk.allTicket.query"}, {"url": "/api/maintenance-manager/v1/risk/deal-flow", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.query:operation.uedm.maintenance.f.risk.toBeHandled.query:operation.uedm.maintenance.f.risk.allTicket.query"}, {"url": "/api/maintenance-manager/v1/risk/queryDetails", "method": "GET", "operation": "operation.uedm.maintenance.f.risk.initiated.query:operation.uedm.maintenance.f.risk.toBeHandled.query:operation.uedm.maintenance.f.risk.handled.query:operation.uedm.maintenance.f.risk.allTicket.query"}, {"url": "/api/maintenance-manager/v1/risk/name-check", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.query"}, {"url": "/api/maintenance-manager/v1/risk/temporary", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.query"}, {"url": "/api/maintenance-manager/v1/risk/add", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.query"}, {"url": "/api/maintenance-manager/v1/risk/delete-by-id", "method": "DELETE", "operation": "operation.uedm.maintenance.f.risk.initiated.delete"}, {"url": "/api/maintenance-manager/v1/risk/exportListCreated", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.initiated.export"}, {"url": "/api/maintenance-manager/v1/risk/queryListPending", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.toBeHandled.query"}, {"url": "/api/maintenance-manager/v1/risk/exportListPending", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.toBeHandled.export"}, {"url": "/api/maintenance-manager/v1/risk/queryListHandled", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.handled.query"}, {"url": "/api/maintenance-manager/v1/risk/exportListHandled", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.handled.export"}, {"url": "/api/maintenance-manager/v1/risk/queryListAll", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.allTicket.query"}, {"url": "/api/maintenance-manager/v1/risk/exportListAll", "method": "POST", "operation": "operation.uedm.maintenance.f.risk.allTicket.export"}, {"url": "/api/maintenance-manager/v1/duty/getOverviewCalendarAll", "method": "GET", "operation": "operation.uedm.duty.f.manage.overview"}, {"url": "/api/maintenance-manager/v1/duty/getDutyMemberTodayNow", "method": "GET", "operation": "operation.uedm.duty.f.manage.overview"}, {"url": "/api/maintenance-manager/v1/duty/getHandoverTaskByName", "method": "GET", "operation": "operation.uedm.duty.f.manage.overview"}, {"url": "/api/maintenance-manager/v1/duty/handover/query/details", "method": "GET", "operation": "operation.uedm.duty.f.manage.overview:operation.uedm.duty.f.manage.handover.deliver:operation.uedm.duty.f.manage.handover.heritage:operation.uedm.duty.f.manage.handover.detail"}, {"url": "/api/maintenance-manager/v1/template/getDutyTemplateByPlanId", "method": "GET", "operation": "operation.uedm.duty.f.manage.overview.deliver:operation.uedm.duty.f.manage.overview.heritage:operation.uedm.duty.f.manage.handover.deliver:operation.uedm.duty.f.manage.handover.heritage"}, {"url": "/api/maintenance-manager/v1/duty/handover/deliver", "method": "POST", "operation": "operation.uedm.duty.f.manage.overview.deliver:operation.uedm.duty.f.manage.handover.deliver"}, {"url": "/api/maintenance-manager/v1/duty/handover/heritage", "method": "POST", "operation": "operation.uedm.duty.f.manage.overview.heritage:operation.uedm.duty.f.manage.handover.heritage"}, {"url": "/api/maintenance-manager/v1/duty-shift/add", "method": "POST", "operation": "operation.uedm.duty.f.manage.config.order.add"}, {"url": "/api/maintenance-manager/v1/duty-shift/delete", "method": "DELETE", "operation": "operation.uedm.duty.f.manage.config.order.delete"}, {"url": "/api/maintenance-manager/v1/duty-shift/details", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.order.edit:operation.uedm.duty.f.manage.config.order.detail"}, {"url": "/api/maintenance-manager/v1/duty-shift/update", "method": "PUT", "operation": "operation.uedm.duty.f.manage.config.order.edit"}, {"url": "/api/maintenance-manager/v1/duty-shift/querylist", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.order.query"}, {"url": "/api/maintenance-manager/v1/duty-group/add", "method": "POST", "operation": "operation.uedm.duty.f.manage.config.group.add"}, {"url": "/api/maintenance-manager/v1/malfunction/getFuzzyUsers", "method": "POST", "operation": "operation.uedm.duty.f.manage.config.group.add:operation.uedm.duty.f.manage.config.group.edit"}, {"url": "/api/maintenance-manager/v1/duty-group/delete", "method": "DELETE", "operation": "operation.uedm.duty.f.manage.config.group.delete"}, {"url": "/api/maintenance-manager/v1/duty-group/details", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.group.edit"}, {"url": "/api/maintenance-manager/v1/duty-group/update", "method": "PUT", "operation": "operation.uedm.duty.f.manage.config.group.edit"}, {"url": "/api/maintenance-manager/v1/duty-group/querylist", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.group.query"}, {"url": "/api/maintenance-manager/v1/configuration/getTimeTag", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.limit.edit"}, {"url": "/api/maintenance-manager/v1/configuration/updateTimeTag", "method": "PUT", "operation": "operation.uedm.duty.f.manage.config.limit.edit"}, {"url": "/api/maintenance-manager/v1/template/getAllDutyTemplate", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.template"}, {"url": "/api/maintenance-manager/v1/template/checkAddTemplateName", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.template.add"}, {"url": "/api/maintenance-manager/v1/template/addDutyTemplate", "method": "POST", "operation": "operation.uedm.duty.f.manage.config.template.add"}, {"url": "/api/maintenance-manager/v1/template/checkModifyTemplateName", "method": "GET", "operation": "operation.uedm.duty.f.manage.config.template.edit"}, {"url": "/api/maintenance-manager/v1/template/modifyDutyTemplate", "method": "PUT", "operation": "operation.uedm.duty.f.manage.config.template.edit"}, {"url": "/api/maintenance-manager/v1/template/deleteDutyTemplate", "method": "DELETE", "operation": "operation.uedm.duty.f.manage.config.template.delete"}, {"url": "/api/maintenance-manager/v1/plan/checkAddPlanName", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add"}, {"url": "/api/maintenance-manager/v1/plan/addDutyPlan", "method": "POST", "operation": "operation.uedm.duty.f.manage.plan.edit.add"}, {"url": "/api/maintenance-manager/v1/plan/getDutyGroupAll", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/getPersonExceptTest", "method": "POST", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/template/getDutyTemplateName", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/getShiftRule", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/getShiftInfoById", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/getGroupPersonById", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.add:operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/removeDutyPlanById", "method": "DELETE", "operation": "operation.uedm.duty.f.manage.plan.edit.delete"}, {"url": "/api/maintenance-manager/v1/plan/getDutyPlanById", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.edit:operation.uedm.duty.f.manage.plan.edit.detail"}, {"url": "/api/maintenance-manager/v1/plan/checkModifyPlanName", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/modifyDutyPlanById", "method": "PUT", "operation": "operation.uedm.duty.f.manage.plan.edit.edit"}, {"url": "/api/maintenance-manager/v1/plan/getDutyPlanByPageNum", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.edit.query"}, {"url": "/api/maintenance-manager/v1/plan/modifyStatusById", "method": "PUT", "operation": "operation.uedm.duty.f.manage.plan.edit.start:operation.uedm.duty.f.manage.plan.edit.stop"}, {"url": "/api/maintenance-manager/v1/duty/getPlanDutyNameAllAndMessage", "method": "GET", "operation": "operation.uedm.duty.f.manage.plan.calendar"}, {"url": "/api/maintenance-manager/v1/duty/handover/query", "method": "POST", "operation": "operation.uedm.duty.f.manage.handover.query"}, {"url": "/api/maintenance-manager/v1/notice/export", "method": "POST", "operation": "operation.uedm.workbench.overview.notice.export"}, {"url": "/api/maintenance-manager/v1/notice/published-user", "method": "POST", "operation": "operation.uedm.workbench.overview.notice.show"}, {"url": "/api/maintenance-manager/v1/worktable/getAllUserName", "method": "GET", "operation": "operation.uedm.workbench.overview.plan.calendar"}, {"url": "/api/maintenance-manager/v1/worktable/getDutyCondition", "method": "GET", "operation": "operation.uedm.workbench.overview.plan.calendar"}, {"url": "/api/maintenance-manager/v1/workbench/get_flow_statistics_data", "method": "POST", "operation": "operation.uedm.workbench.overview.process.statistics"}, {"url": "/api/maintenance-manager/v1/workbench/web-output", "method": "POST", "operation": "operation.uedm.workbench.me.process.export"}, {"url": "/api/maintenance-manager/v1/workbench/my-process", "method": "POST", "operation": "operation.uedm.workbench.me.process.select"}, {"url": "/api/maintenance-manager/v1/notice/published", "method": "POST", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/draft", "method": "POST", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/draft-detail", "method": "GET", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/documented", "method": "POST", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/documented-detail", "method": "GET", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/add", "method": "POST", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/notice/modify-or-publish", "method": "PUT", "operation": "operation.uedm.workbench.notice.manage"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/query", "method": "POST", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/check-template-name", "method": "GET", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/check-topology-alarm-config", "method": "GET", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/getTopology", "method": "GET", "operation": "operation.uedm.maintenance.f.drill.template.management:operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/add", "method": "POST", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/record/start", "method": "POST", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/record/interrupt", "method": "POST", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/detail/query", "method": "GET", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/get-update-info", "method": "GET", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/template/edit", "method": "POST", "operation": "operation.uedm.maintenance.f.drill.template.management"}, {"url": "/api/maintenance-manager/v1/emergency-drill/record/query", "method": "GET", "operation": "operation.uedm.maintenance.f.emergency.drill.record"}, {"url": "/api/maintenance-manager/v1/emergency-drill/record/detail/query/condition", "method": "POST", "operation": "operation.uedm.maintenance.f.emergency.drill.record"}, {"url": "/api/maintenance-manager/v1/emergency-drill/record/detail/query", "method": "GET", "operation": "operation.uedm.maintenance.f.emergency.drill.record"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/list", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.query"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/alarm-malfunction", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.query"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/detailById", "method": "GET", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.query"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/name", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.add:operation.uedm.config.forward.b.alarmToMalfunction.mod"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/add", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.add"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/update", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.mod"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/delete", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.delete"}, {"url": "/api/maintenance-manager/v1/alarm-malfunction/status", "method": "POST", "operation": "operation.uedm.config.forward.b.alarmToMalfunction.enable"}]