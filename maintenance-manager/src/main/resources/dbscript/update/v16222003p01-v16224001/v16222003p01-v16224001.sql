-- 维保项表
CREATE TABLE IF NOT EXISTS maintenance_item(
                                               id text NOT NULL,
                                               maintenance_template_id text NOT NULL,
                                               name text NOT NULL,
                                               content varchar(2000) NOT NULL ,
                                               attachment TEXT,
                                               PRIMARY KEY (id));

COMMENT ON TABLE  maintenance_item IS '维保项表';
COMMENT ON COLUMN  maintenance_item.id IS '维保项表id';
COMMENT ON COLUMN  maintenance_item.maintenance_template_id IS '维保模板id';
COMMENT ON COLUMN  maintenance_item.name IS '维保项名称';
COMMENT ON COLUMN  maintenance_item.content IS '维保内容  2000字以内';
COMMENT ON COLUMN  maintenance_item.attachment IS '附件';
-- 索引(维保模板id)
CREATE INDEX IF NOT EXISTS maintenance_item_template_id ON maintenance_item (maintenance_template_id);


-- 维保模板表
CREATE TABLE IF NOT EXISTS maintenance_template(
                                                   id text NOT NULL,
                                                   version smallint NOT NULL,
                                                   base_version_id text NOT NULL,
                                                   name text NOT NULL,
                                                   maintenance_obj_type text NOT NULL,
                                                   creator text NOT NULL,
                                                   updater text,
                                                   gmt_create  text NOT NULL,
                                                   gmt_modified text,
                                                   PRIMARY KEY (id));

COMMENT ON TABLE   maintenance_template IS '维保模板表';
COMMENT ON COLUMN  maintenance_template.id IS '维保模板表id';
COMMENT ON COLUMN  maintenance_template.version IS '维保模板版本';
COMMENT ON COLUMN  maintenance_template.base_version_id IS '维保模板基本版本id';
COMMENT ON COLUMN  maintenance_template.name IS '维保模板名称';
COMMENT ON COLUMN  maintenance_template.maintenance_obj_type IS '维保对象类型监控对象存moc，监控设备存moduleId';
COMMENT ON COLUMN  maintenance_template.creator IS '创建人';
COMMENT ON COLUMN  maintenance_template.updater IS '更新人';
COMMENT ON COLUMN  maintenance_template.gmt_create IS '创建时间';
COMMENT ON COLUMN  maintenance_template.gmt_modified IS '修改时间';
-- 索引(维保模板基础版本id)
CREATE INDEX IF NOT EXISTS maintenance_template_base_version ON maintenance_template (base_version_id);

-- 维保对象表
CREATE TABLE IF NOT EXISTS maintenance_object(
                                                 id text NOT NULL,
                                                 maintenance_plan_id text NOT NULL,
                                                 instance_id text NOT NULL,
                                                 instance_name text NOT NULL,
                                                 type text NOT NULL,
                                                 resource_type text NOT NULL,
                                                 template text NOT NULL,
                                                 PRIMARY KEY (id));

COMMENT ON TABLE  maintenance_object IS '维保对象表';
COMMENT ON COLUMN  maintenance_object.id IS '维保对象表id';
COMMENT ON COLUMN  maintenance_object.maintenance_plan_id IS '维保计划id';
COMMENT ON COLUMN  maintenance_object.instance_id IS '维保对象实例id';
COMMENT ON COLUMN  maintenance_object.instance_name IS '维保对象实例名称';
COMMENT ON COLUMN  maintenance_object.type IS '维保对象类型';
COMMENT ON COLUMN  maintenance_object.type IS '维保对象资源类型';
COMMENT ON COLUMN  maintenance_object.template IS '维保对象选择的维保模板 json';
-- 索引(维保计划id)
CREATE INDEX IF NOT EXISTS maintenance_object_maintenance_plan_id ON maintenance_object (maintenance_plan_id);


-- 计划表
CREATE TABLE IF NOT EXISTS maintenance_plan(
                                               id text NOT NULL,
                                               version smallint NOT NULL,
                                               base_version_id text NOT NULL,
                                               name text NOT NULL,
                                               maintenance_plan_type smallint NOT NULL,
                                               responsible_department text NOT NULL,
                                               responsible_person text NOT NULL,
                                               execute_department text NOT NULL,
                                               executor text,
                                               plan_begin_time text NOT NULL,
                                               plan_end_time text NOT NULL,
                                               reminder_mode smallint NOT NULL,
                                               status smallint NOT NULL,
                                               description text NOT NULL,
                                               creator text NOT NULL,
                                               updater text,
                                               last_task_generate_time text,
                                               execute_rule text NOT NULL,
                                               submit_json_md5 text NOT NULL,
                                               gmt_create  text NOT NULL,
                                               gmt_modified text,
                                               PRIMARY KEY (id));

COMMENT ON TABLE   maintenance_plan IS '维保计划表';
COMMENT ON COLUMN  maintenance_plan.id IS '维保计划表id';
COMMENT ON COLUMN  maintenance_plan.version IS '维保计划表版本';
COMMENT ON COLUMN  maintenance_plan.base_version_id IS '维保计划表基本版本id';
COMMENT ON COLUMN  maintenance_plan.name IS '维保计划表名称';
COMMENT ON COLUMN  maintenance_plan.maintenance_plan_type IS '维保计划类型1日常维保 2临时维保 3健康检查';
COMMENT ON COLUMN  maintenance_plan.responsible_department IS '责任部门';
COMMENT ON COLUMN  maintenance_plan.responsible_person IS '责任人';
COMMENT ON COLUMN  maintenance_plan.execute_department IS '执行部门';
COMMENT ON COLUMN  maintenance_plan.executor IS '执行人';
COMMENT ON COLUMN  maintenance_plan.plan_begin_time IS '维保计划开始时间';
COMMENT ON COLUMN  maintenance_plan.plan_end_time IS '维保计划结束时间';
COMMENT ON COLUMN  maintenance_plan.reminder_mode IS '提醒方式 1无  2短信 3邮件';
COMMENT ON COLUMN  maintenance_plan.status IS '维保计划状态 1未启用 2启用 3暂停 4终止';
COMMENT ON COLUMN  maintenance_plan.description IS '描述';
COMMENT ON COLUMN  maintenance_plan.creator IS '创建人';
COMMENT ON COLUMN  maintenance_plan.updater IS '修改人';
COMMENT ON COLUMN  maintenance_plan.last_task_generate_time IS '最后一次任务生成时间';
COMMENT ON COLUMN  maintenance_plan.execute_rule IS '执行规则 json';
COMMENT ON COLUMN  maintenance_plan.submit_json_md5 IS '前端提交数据时排除id的json';
COMMENT ON COLUMN  maintenance_plan.gmt_create IS '创建时间';
COMMENT ON COLUMN  maintenance_plan.gmt_modified IS '修改时间';
-- 索引(维保计划基础版本id)
CREATE INDEX IF NOT EXISTS maintenance_plan_base_version ON maintenance_plan (base_version_id);


-- 维保任务表
CREATE TABLE IF NOT EXISTS maintenance_task(
                                               id text NOT NULL,
                                               maintenance_plan_id text NOT NULL,
                                               name text NOT NULL,
                                               status smallint NOT NULL,
                                               pre_status boolean,
                                               task_begin_time text NOT NULL,
                                               task_end_time text NOT NULL,
                                               complete boolean NOT NULL,
                                               conclusion smallint NOT NULL,
                                               download boolean NOT NULL,
                                               executor text,
                                               execute_time text,
                                               PRIMARY KEY (id));

COMMENT ON TABLE  maintenance_task IS '维保任务表';
COMMENT ON COLUMN  maintenance_task.id IS '维保任务表id';
COMMENT ON COLUMN  maintenance_task.maintenance_plan_id IS '维保计划id';
COMMENT ON COLUMN  maintenance_task.name IS '维保任务名称';
COMMENT ON COLUMN  maintenance_task.status IS '维保任务状态 1待执行 2执行中 3已完成 4超期待执行 5超期完成 6超期未完成';
COMMENT ON COLUMN  maintenance_task.pre_status IS '前置状态是否为待执行(true为待执行)';
COMMENT ON COLUMN  maintenance_task.task_begin_time IS '维保任务开始时间';
COMMENT ON COLUMN  maintenance_task.task_end_time IS '维保任务结束时间';
COMMENT ON COLUMN  maintenance_task.complete IS '任务是否完成 true 已完成 false未完成';
COMMENT ON COLUMN  maintenance_task.conclusion IS '任务结论  1正常 2异常 3不涉及';
COMMENT ON COLUMN  maintenance_task.download IS '是否被下载 ';
COMMENT ON COLUMN  maintenance_task.executor IS '最终执行人';
COMMENT ON COLUMN  maintenance_task.execute_time IS '维保任务最终提交时间';
-- 索引(维保计划id)
CREATE INDEX IF NOT EXISTS maintenance_task_maintenance_plan_id ON maintenance_task (maintenance_plan_id);

-- 维保结果表
CREATE TABLE IF NOT EXISTS maintenance_result(
                                                 id text NOT NULL,
                                                 status smallint NOT NULL,
                                                 maintenance_task_id text NOT NULL,
                                                 maintenance_obj_id text NOT NULL,
                                                 maintenance_template_id text NOT NULL,
                                                 maintenance_item_id text NOT NULL,
                                                 complete boolean,
                                                 conclusion smallint NOT NULL,
                                                 abnormal_content text,
                                                 attachment text,
                                                 PRIMARY KEY (id));

COMMENT ON TABLE  maintenance_result IS '维保结果表';
COMMENT ON COLUMN  maintenance_result.id IS '维保结果表id';
COMMENT ON COLUMN  maintenance_result.status IS '状态 1暂存 2已提交';
COMMENT ON COLUMN  maintenance_result.maintenance_task_id IS '维保任务id';
COMMENT ON COLUMN  maintenance_result.maintenance_obj_id IS '维保对象id';
COMMENT ON COLUMN  maintenance_result.maintenance_template_id IS '维保模板id';
COMMENT ON COLUMN  maintenance_result.maintenance_item_id IS '维保项id';
COMMENT ON COLUMN  maintenance_result.complete IS '任务是否完成 true 已完成 false未完成';
COMMENT ON COLUMN  maintenance_result.conclusion IS '任务结论  1正常 2异常 3不涉及';
COMMENT ON COLUMN  maintenance_result.abnormal_content IS '异常内容';
COMMENT ON COLUMN  maintenance_result.attachment IS '附件';

-- 索引(维保任务id)
CREATE INDEX IF NOT EXISTS maintenance_result_maintenance_task_id ON maintenance_result (maintenance_task_id);