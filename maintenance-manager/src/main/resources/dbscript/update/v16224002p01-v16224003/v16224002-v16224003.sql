CREATE TABLE IF NOT EXISTS workflow_instance_risk(
                                           id text NOT NULL,
                                           type text NOT NULL,
                                           workflow_id text,
                                           code text NOT NULL,
                                           name text NOT NULL,
                                           status smallint NOT NULL ,
                                           handler text,
                                           creator text NOT NULL ,
                                           gmt_create text NOT NULL ,
                                           describe text,
                                           attachment TEXT,
                                           flexible_str1 text,
                                           flexible_str2 text,
                                           flexible_str3 text,
                                           flexible_str4 text,
                                           flexible_str5 text,
                                           flexible_str6 text,
                                           flexible_str7 text,
                                           flexible_str8 text,
                                           flexible_str9 text,
                                           flexible_str10 text,
                                           flexible_str11 text,
                                           flexible_str12 text,
                                           flexible_str13 text,
                                           flexible_str14 text,
                                           flexible_str15 text,
                                           flexible_str16 text,
                                           flexible_str17 text,
                                           flexible_str18 text,
                                           flexible_str19 text,
                                           flexible_str20 text,
                                           flexible_int1 int,
                                           flexible_int2 int,
                                           flexible_int3 int,
                                           flexible_int4 int,
                                           flexible_int5 int,
                                           flexible_int6 int,
                                           flexible_int7 int,
                                           flexible_int8 int,
                                           flexible_int9 int,
                                           flexible_int10 int,
                                           PRIMARY KEY (id));

COMMENT ON TABLE  workflow_instance_risk IS '单据实体表';
COMMENT ON COLUMN  workflow_instance_risk.id IS '单据id';
COMMENT ON COLUMN  workflow_instance_risk.type IS '单据类型';
COMMENT ON COLUMN  workflow_instance_risk.workflow_id IS '单据流程id';
COMMENT ON COLUMN  workflow_instance_risk.code IS '单号';
COMMENT ON COLUMN  workflow_instance_risk.name IS '单据名称';
COMMENT ON COLUMN  workflow_instance_risk.status IS '状态(0:拟制;1:待处理;2:处理中;3:挂起;4:完结;5:取消)';
COMMENT ON COLUMN  workflow_instance_risk.creator IS '创建人';
COMMENT ON COLUMN  workflow_instance_risk.gmt_create IS '创建时间';
COMMENT ON COLUMN  workflow_instance_risk.describe IS '描述';
COMMENT ON COLUMN  workflow_instance_risk.attachment IS '附件';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str1 IS '灵活字符字段1';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str2 IS '灵活字符字段2';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str3 IS '灵活字符字段3';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str4 IS '灵活字符字段4';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str5 IS '灵活字符字段5';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str6 IS '灵活字符字段6';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str7 IS '灵活字符字段7';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str8 IS '灵活字符字段8';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str9 IS '灵活字符字段9';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str10 IS '灵活字符字段10';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str11 IS '灵活字符字段11';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str12 IS '灵活字符字段12';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str13 IS '灵活字符字段13';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str14 IS '灵活字符字段14';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str15 IS '灵活字符字段15';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str16 IS '灵活字符字段16';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str17 IS '灵活字符字段17';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str18 IS '灵活字符字段18';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str19 IS '灵活字符字段19';
COMMENT ON COLUMN  workflow_instance_risk.flexible_str20 IS '灵活字符字段20';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int1 IS '灵活数值字段1';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int2 IS '灵活数值字段2';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int3 IS '灵活数值字段3';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int4 IS '灵活数值字段4';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int5 IS '灵活数值字段5';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int6 IS '灵活数值字段6';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int7 IS '灵活数值字段7';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int8 IS '灵活数值字段8';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int9 IS '灵活数值字段9';
COMMENT ON COLUMN  workflow_instance_risk.flexible_int10 IS '灵活数值字段10';
-- 索引(风险单号)
CREATE INDEX IF NOT EXISTS code ON workflow_instance_risk (code);

CREATE TABLE IF NOT EXISTS duty_order_info
(
    id VARCHAR(128) NOT NULL,
    order_name VARCHAR(255) NOT NULL,
    order_description text,
    order_creater VARCHAR(50) NOT NULL,
    order_create_time VARCHAR(23) NOT NULL,
    order_updater VARCHAR(50),
    order_update_time VARCHAR(23),
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_order_info IS '值班规则';
COMMENT ON COLUMN duty_order_info.id IS '规则编号';
COMMENT ON COLUMN duty_order_info.order_name IS '班次规则名称';
COMMENT ON COLUMN duty_order_info.order_description IS '班次规则描述';
COMMENT ON COLUMN duty_order_info.order_creater IS '创建人';
COMMENT ON COLUMN duty_order_info.order_create_time IS '创建时间';
COMMENT ON COLUMN duty_order_info.order_updater IS '修改人';
COMMENT ON COLUMN duty_order_info.order_update_time IS '修改时间';


CREATE TABLE IF NOT EXISTS duty_order_time
(
    id VARCHAR(128) NOT NULL,
    order_info_id VARCHAR(128) NOT NULL,
    order_time_name VARCHAR(255) NOT NULL,
    order_begin_time VARCHAR(23) NOT NULL,
    order_end_time VARCHAR(23) NOT NULL,
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_order_time IS '班次时间';
COMMENT ON COLUMN duty_order_time.id IS '班次时间编号';
COMMENT ON COLUMN duty_order_time.order_info_id IS '值班规则编号';
COMMENT ON COLUMN duty_order_time.order_time_name IS '班次名称';
COMMENT ON COLUMN duty_order_time.order_begin_time IS '班次开始时间';
COMMENT ON COLUMN duty_order_time.order_end_time IS '班次结束时间';

CREATE INDEX IF NOT EXISTS idx_order_info_id on duty_order_time(order_info_id);


CREATE TABLE IF NOT EXISTS duty_group
(
    id VARCHAR(128) NOT NULL,
    group_name VARCHAR(255) NOT NULL,
    group_member text[] NOT NULL,
    group_creater VARCHAR(50) NOT NULL,
    group_create_time VARCHAR(23) NOT NULL,
    group_updater VARCHAR(50),
    group_update_time VARCHAR(23),
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_group IS '值班班组';
COMMENT ON COLUMN duty_group.id IS '班组编号';
COMMENT ON COLUMN duty_group.group_name IS '值班组名称';
COMMENT ON COLUMN duty_group.group_member IS '值班人员';
COMMENT ON COLUMN duty_group.group_creater IS '创建人';
COMMENT ON COLUMN duty_group.group_create_time IS '创建时间';
COMMENT ON COLUMN duty_group.group_updater IS '修改人';
COMMENT ON COLUMN duty_group.group_update_time IS '修改时间';


CREATE TABLE IF NOT EXISTS duty_handover_limit
(
    handover_deliver_limit integer default 30 NOT NULL,
    handover_heritage_limit integer default 30 NOT NULL,
    handover_update_time VARCHAR(23)
    );

COMMENT ON TABLE duty_handover_limit IS '交接班时限配置';
COMMENT ON COLUMN duty_handover_limit.handover_deliver_limit IS '正常交班时间范围';
COMMENT ON COLUMN duty_handover_limit.handover_heritage_limit IS '正常接班时间范围';
COMMENT ON COLUMN duty_handover_limit.handover_update_time IS '修改时间';

INSERT INTO duty_handover_limit SELECT 30, 30, null WHERE NOT EXISTS (select * FROM duty_handover_limit);


CREATE TABLE IF NOT EXISTS duty_plan
(
    id VARCHAR(128) NOT NULL,
    plan_name VARCHAR(255) NOT NULL,
    order_info_id VARCHAR(128) NOT NULL,
    group_id VARCHAR(128) NOT NULL,
    plan_temporary_member text[],
    plan_creater VARCHAR(50) NOT NULL,
    plan_create_time VARCHAR(23) NOT NULL,
    plan_update_time VARCHAR(23) NOT NULL,
    plan_start_time VARCHAR(23) NOT NULL,
    plan_end_time VARCHAR(23) NOT NULL,
    plan_status smallint NOT NULL,
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_plan IS '值班计划';
COMMENT ON COLUMN duty_plan.id IS '计划编号';
COMMENT ON COLUMN duty_plan.plan_name IS '计划名称';
COMMENT ON COLUMN duty_plan.order_info_id IS '值班规则编号';
COMMENT ON COLUMN duty_plan.group_id IS '值班组编号';
COMMENT ON COLUMN duty_plan.plan_temporary_member IS '临时调配人员';
COMMENT ON COLUMN duty_plan.plan_creater IS '创建人';
COMMENT ON COLUMN duty_plan.plan_create_time IS '创建时间';
COMMENT ON COLUMN duty_plan.plan_update_time IS '修改时间';
COMMENT ON COLUMN duty_plan.plan_start_time IS '计划开始时间';
COMMENT ON COLUMN duty_plan.plan_end_time IS '计划结束时间';
COMMENT ON COLUMN duty_plan.plan_status IS '计划状态';


CREATE TABLE IF NOT EXISTS duty_plan_detail
(
    id VARCHAR(128) NOT NULL,
    plan_id VARCHAR(128)  NOT NULL,
    plan_detail_date date NOT NULL,
    order_time_id VARCHAR(128)  NOT NULL,
    duty_member text[],
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_plan_detail IS '值班计划详情';
COMMENT ON COLUMN duty_plan_detail.id IS '计划详情编号';
COMMENT ON COLUMN duty_plan_detail.plan_id IS '计划编号';
COMMENT ON COLUMN duty_plan_detail.plan_detail_date IS '计划详情日期';
COMMENT ON COLUMN duty_plan_detail.order_time_id IS '班次时间编号';
COMMENT ON COLUMN duty_plan_detail.duty_member IS '值班人员';

CREATE INDEX IF NOT EXISTS idx_plan_id on duty_plan_detail(plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_detail_date on duty_plan_detail(plan_detail_date);


CREATE TABLE IF NOT EXISTS duty_handover
(
    id VARCHAR(128) NOT NULL,
    deliver_name text,
    plan_id VARCHAR(128),
    plan_name VARCHAR(255) NOT NULL,
    deliver_order_id VARCHAR(128),
    deliver_order_name  VARCHAR(255),
    deliver_time VARCHAR(23),
    deliver_description text,
    deliver_status smallint,
    heritage_name text,
    heritage_order_name VARCHAR(255),
    heritage_time VARCHAR(23),
    heritage_description text,
    heritage_status smallint,
    handover_status smallint NOT NULL,
    handover_type smallint NOT NULL,
    PRIMARY KEY (id)
    );

COMMENT ON TABLE duty_handover IS '交接班';
COMMENT ON COLUMN duty_handover.id IS '交接班编号';
COMMENT ON COLUMN duty_handover.deliver_name IS '交班人';
COMMENT ON COLUMN duty_handover.plan_id IS '计划id';
COMMENT ON COLUMN duty_handover.plan_name IS '计划名称';
COMMENT ON COLUMN duty_handover.deliver_order_id IS '交班班次id';
COMMENT ON COLUMN duty_handover.deliver_order_name IS '交班班次名称';
COMMENT ON COLUMN duty_handover.deliver_time IS '交班时间';
COMMENT ON COLUMN duty_handover.deliver_description IS '交班描述';
COMMENT ON COLUMN duty_handover.deliver_status IS '交班状态';
COMMENT ON COLUMN duty_handover.heritage_name IS '接班人';
COMMENT ON COLUMN duty_handover.heritage_order_name IS '接班班次名称';
COMMENT ON COLUMN duty_handover.heritage_time IS '接班时间';
COMMENT ON COLUMN duty_handover.heritage_description IS '接班描述';
COMMENT ON COLUMN duty_handover.heritage_status IS '接班状态';
COMMENT ON COLUMN duty_handover.handover_status IS '交接班状态';
COMMENT ON COLUMN duty_handover.handover_type IS '交接班类型';

CREATE INDEX IF NOT EXISTS idx_deliver_name on duty_handover(deliver_name);
CREATE INDEX IF NOT EXISTS idx_heritage_name on duty_handover(heritage_name);
CREATE INDEX IF NOT EXISTS idx_handover_status on duty_handover(handover_status);