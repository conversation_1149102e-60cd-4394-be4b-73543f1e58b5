debug: false
dexcloud:
  rpc:
    httpconf:
      readTimeout: 180000
      connectTimeout: 18000
      writeTimeout: 18000
      maxIdleConnections: 100 #连接池保留idle连接的最大数目
      keepAliveDurationMinutes: 10 #每个idle连接保留的最大时间，单位是分钟
    retrofit:
      enabled: true
    circuitbreakerconf:
      isOn: false
  configclient:
    enabled: true
  base:
    microservice:
      name: maintenance-manager
      version: v1
      organization: uedm
      metaInfo:
        scope: test
  serviceinfo:
    serviceName: uedm-maintenance   #  应用服务名称
  web:
    swagger:
      beanConfig:
        title: 'UEDM maintenance-manager API Documentation'
        version: '1.0'
  ftpsrvice:
    type: sftp
    pasv: true
    #    userName: ${ftpservice_userName}
    #    userPassword: ${ftpservice_ftpServiceConfig_userPassword}
    #    ftpServerAddress: ${ftpservice_ftpServerAddress}
    #    ftpServerPort: ${ftpservice_ftpServerPort}
    #    ftpsServerAddress: ${ftpservice_ftpsServerAddress}
    #    ftpsServerPort: ${ftpservice_ftpsServerPort}
    sftpServerAddress: test-ftpservice_sftpsServerAddress
  #    sftpServerPort: ${ftpservice_sftpServerPort}
  discovery:
    msb:
      enabled: true
      server:
        address: ${msb_svrIp}
        port: ${msb_svrPort}
        namespace: ${msb_nameSpace}
      client:
        cache:
          enabled: false
        registry:
          enabled: false
  redis:
    redisson:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      sentinelHost: ${redis_sentinel_host:}
      sentinelPort: ${redis_sentinel_port:}
      masterName: ${redis_sentinel_mastername:}
      poolSize: 32
      poolMinIdleSize: 4
      slavePoolSize: 2
      slavePoolMinIdleSize: 1
      dnsMonitoringInterval: 5000
      readMode: MASTER
      scanInterval: 1000
      timeout: 3000
      connectTimeout: 10000
      idleConnectionTimeout: 10000
      retryAttempts: 3
      retryInterval: 1500
server:
  port: 29108
  jetty:
    acceptors: 1
    selectors: 2
    threadpool:
      maxThreads: 64
      minThreads: 16
spring:
  cache:
    type: caffeine
  activiti:
    history-level: full
  application:
    name: uedm-maintenance-maintenance-manager
  datasource:
    url: jdbc:postgresql://${rdb_ip}:${rdb_port}/${rdb_dbname}?stringtype=unspecified
    username: ${rdb_user}
    password: ${rdb_password}
    driver-class-name: ${rdb_driver}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximumPoolSize: 20
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jersey:
    application-path: /api/maintenance-manager/v1
  cloud:
    config:
      discovery:
        enabled: false
      profile: prod
      label: master
      fail-fast: false
      uri: http://localhost:${server.port}
    zookeeper:
      enabled: false
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  mapper-locations: classpath:mapper/*.xml
kafkaclientconf:
  zkServers: ${kafka_zk_addresses}:${kafka_zk_port}
  bootstrapServers:  ${kafka_brokers}:${kafka_port}
  kafkaServiceName: kafka
  kafkaServiceVersion: v1
  consumerConf:
    properties:
      value.deserializer: org.apache.kafka.common.serialization.StringDeserializer
  producerConf:
    properties:
      key.serializer: org.apache.kafka.common.serialization.StringSerializer
      value.serializer: org.apache.kafka.common.serialization.StringSerializer
  zkClientConf:
    sessionTimeout: 15000
    connectionTimeout: 10000
    zkSerializer: org.dexcloud.springboot.kafka.serializer.StringZkSerializer
  zkUtilsConf:
    secure: false
uedm:
  cache:
    caches:
  common:
    common-msb:
      enable: true
      times: 100           #请求重试次数
      services: #依赖的微服务列表
        - config-manager   #微服务唯一标识，请使用common包com.zte.uedm.basis.cons.MicroServiceOptional中定义的微服务id
        - backup-service
    common-db:
      enable: true
      forced-install: false
      module: maintenance-manager
      current-version: @revision@
      base-version: v1
    common-caffeine:
      enable: true
      caches:
        - name: CACHE_NAME_MOC_INFO  # 模型信息缓存
          maxSize: 500
        - name: CACHE_NAME_GROUP_INSTANCE # 分组实例缓存
          maxSize: 30000
        - name: CACHE_NAME_FIELD_INSTANCE  #场站实例缓存
          maxSize: 30000
        - name: CACHE_NAME_DEVICE_INSTANCE  #设备实例缓存
          maxSize: 900000
        - name: CACHE_NAME_COLLECTOR_INSTANCE  #采集器实例缓存
          maxSize: 30000
        - name: CACHE_NAME_RESOURCE_COLLECTOR_RELATION_INSTANCE # 采集器与设备关系缓存
          maxSize: 900000
        - name: CACHE_NAME_STANDARD_POINT_INSTANCE # 模型标准测点
          maxSize: 500000
        - name: CACHE_NAME_ADAPTER_INSTANCE # 适配组件
          maxSize: 500000
    common-config:
      enable: true
    common-kafka:
      producer:
        enable: true
      consumer:
        enable: true
        auto-offset-reset: latest
        group-id: UEDM_MAINTENANCE_LAYER
        enable-auto-commit: false
        topics:
          - uedm_config_change
          - uedm_configuration_change
          - uedm_zenap-licensecenter-lcs-change
          - imop_zenap-licensecenter-lcs-change
    common-mp:
      enable: true
    common-log:
      enable: true
  operationTimeout: 48 #操作超时时间（小时）
  component:
    thread:
      enable: true
  thread:
    pool:
      core-pool-size: 20
      max-pool-size: 50
      queue-capacity: 100
      keep-alive-time: 60
      name-prefix: maintenance-manager-executor-
#  db:
#    init:
#      forced-install: false
#      module: monitor-manager
#      current-version: @revision@
#      base-version: v1

#  kafka:
#    consumer:
#      auto-offset-reset: latest
#      group-id: UEDM_INSPECT_LAYER
#      enable-auto-commit: false
#      topics:
#        - uedm_zenap-licensecenter-lcs-change
#        - imop_zenap-licensecenter-lcs-change
##        - standard_realtime_data_alarm_raised
##        - uedm_start_notify
##        - uedm_config_change
##        - uedm_log
##        - FM_NORTH_UP_TOPIC                #实时告警
sha:
  name: SHA-256
postgresql:
  ip: ${rdb_ip}
  port: ${rdb_port}
  db-name: ${rdb_dbname}
  username: ${rdb_user}
  password: ${rdb_password}
  