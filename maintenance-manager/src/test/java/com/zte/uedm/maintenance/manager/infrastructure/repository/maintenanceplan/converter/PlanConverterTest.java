package com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.converter;

import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.po.MaintenancePlanPO;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PlanConverterTest {
    @Test
    public void batchTest(){

        try{
            PlanConverter.fromPlanEntity(null);
        }catch (Exception e){
            Assert.assertNotEquals("aaa",e.getMessage());
        }

        MaintenancePlanPO maintenancePlanPO = PlanConverter.fromPlanEntity(new MaintenancePlanEntity());
        Assert.assertNotNull(maintenancePlanPO);
    }

    @Test
    public void toEntityTest(){
        try{
            PlanConverter.toEntity(null);
        }catch (Exception e){
            Assert.assertNotEquals("aaa",e.getMessage());
        }

        MaintenancePlanEntity maintenancePlanPO = PlanConverter.toEntity(new MaintenancePlanPO());
        Assert.assertNotNull(maintenancePlanPO);
    }

    @Test
    public void toEntityBatchTest(){
        List<MaintenancePlanEntity> maintenancePlanEntities = PlanConverter.toEntityBatch(new ArrayList<>());
        Assert.assertEquals(0,maintenancePlanEntities.size());

        List<MaintenancePlanEntity> maintenancePlanEntities1 = PlanConverter.toEntityBatch(new ArrayList<>(Collections.singletonList(new MaintenancePlanPO())));
        Assert.assertEquals(1,maintenancePlanEntities1.size());
    }
}
