package com.zte.uedm.maintenance.risk.infrastructure.common.util;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class GetMonthOrDataByRangeTest {

    @InjectMocks
    private  GetMonthOrDataByRange getMonthOrDataByRange;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() {
        List<String> listDay = getMonthOrDataByRange.getDayBetween("2022-09-09 15:07:00","2022-09-12 15:07:00");
        List<String> listMon = getMonthOrDataByRange.getMonthBetween("2022-09-09 15:07:00","2022-09-12 15:07:00");
        List<String> assertDay = new ArrayList<>();
        List<String> assertMon = new ArrayList<>();
        assertDay.add("2022-09-09");
        assertDay.add("2022-09-10");
        assertDay.add("2022-09-11");
        assertDay.add("2022-09-12");
        assertMon.add("2022-09");
        Assert.assertEquals(assertDay,listDay);
        Assert.assertEquals(assertMon,listMon);
    }

    @Test
    public void testSingleDay() {
        // 测试单日查询场景
        List<String> listDay = getMonthOrDataByRange.getDayBetween("2025-06-11 00:00:00","2025-06-11 23:59:59");
        List<String> assertDay = new ArrayList<>();
        assertDay.add("2025-06-11");
        Assert.assertEquals("Single day query should return exactly one day", assertDay, listDay);
        Assert.assertEquals("Single day query should return exactly one element", 1, listDay.size());
    }

}