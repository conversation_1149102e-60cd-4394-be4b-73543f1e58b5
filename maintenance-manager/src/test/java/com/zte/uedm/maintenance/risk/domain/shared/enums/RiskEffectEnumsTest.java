package com.zte.uedm.maintenance.risk.domain.shared.enums;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class RiskEffectEnumsTest {
    @Test
    public void riskEffectEnumsTest() {
        RiskEffectEnums[] values = RiskEffectEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            RiskEffectEnums riskEffectEnums = RiskEffectEnums.valueOf(name);
            Assert.assertEquals(name,riskEffectEnums.name());
        }
        Assert.assertEquals(RiskEffectEnums.LOW.getCode().longValue(),0);
        Assert.assertEquals(RiskEffectEnums.MIDDLE.getCode().longValue(),1);
        Assert.assertEquals(RiskEffectEnums.HIGH.getCode().longValue(),2);
    }

    @Test
    public void riskEffectEnumsTest1() {
        List<Integer> codeList = new ArrayList<>();
        Assert.assertEquals(RiskEffectEnums.getCnStringByCode(codeList), "");
    }

    @Test
    public void riskEffectEnumsTest2() {
        List<Integer> codeList = new ArrayList<>();
        codeList.add(0);
        Assert.assertEquals(RiskEffectEnums.getCnStringByCode(codeList), "低");
    }

    @Test
    public void riskEffectEnumsTest3() {
        List<Integer> codeList = new ArrayList<>();
        codeList.add(4);
        Assert.assertEquals(RiskEffectEnums.getCnStringByCode(codeList), "");
    }

    @Test
    public void riskEffectEnumsTest4() {
        List<Integer> codeList = new ArrayList<>();
        Assert.assertEquals(RiskEffectEnums.getEnStringByCode(codeList), "");
    }

    @Test
    public void riskEffectEnumsTest5() {
        List<Integer> codeList = new ArrayList<>();
        codeList.add(0);
        Assert.assertEquals(RiskEffectEnums.getEnStringByCode(codeList), "Low");
    }

    @Test
    public void riskEffectEnumsTest6() {
        List<Integer> codeList = new ArrayList<>();
        codeList.add(4);
        Assert.assertEquals(RiskEffectEnums.getEnStringByCode(codeList), "");
    }


}