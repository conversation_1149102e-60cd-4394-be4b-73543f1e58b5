package com.zte.uedm.maintenance.malfunction.application.scheduled;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.maintenance.common.I18nService;
import com.zte.uedm.maintenance.malfunction.application.executor.MalfunctionCommandService;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.alarmMalfunction.repository.AlarmMalfunctionRuleRepository;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.repository.MalfunctionRepository;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.AlarmService;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.Alarm;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.license.LicenseService;
import com.zte.uedm.maintenance.malfunction.infrastructure.repository.alarmMalfunction.po.AlarmMalfunctionRulePO;
import com.zte.uedm.maintenance.malfunction.interfaces.web.alarmMalfunction.Dto.AlarmMalfunctionRuleDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class AlarmTaskTest {

    @InjectMocks
    private AlarmTask alarmTask;

    @Mock
    private AlarmMalfunctionRuleRepository alarmMalfunctionRuleRepository;

    @Mock
    private AlarmService alarmService;

    @Mock
    private MalfunctionRepository malfunctionRepository;

    @Mock
    private JsonService jsonService;

    @Mock
    private MalfunctionCommandService malfunctionCommandService;

    @Mock
    private ConfigService configService;

    @Mock
    private LicenseService licenseService;

    @Mock
    private I18nService i18nService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void taskCreateMalfunctionTest()
    {
        alarmTask.taskCreateMalfunction();
        Mockito.doReturn(true).when(licenseService).selectMalfunction();
        boolean flag = true;
        try {
            alarmTask.taskCreateMalfunction();
        } catch (Exception e) {
            flag = false;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void dealSingleAlarmTest() throws UedmException {
        Alarm alarm = new Alarm();
        alarm.setMe("1");
        AlarmMalfunctionRuleDto rule = new AlarmMalfunctionRuleDto();
        Map<Integer, Integer> map = new HashMap<>();
        map.put(1, 1);
        rule.setMalfunctionLevel(map);
        rule.setEmergencyLevel(map);
        rule.setEffectLevel(map);
        rule.setMode(2);
        rule.setAlarmMoId("1");
        Mockito.doReturn(new ArrayList<>()).when(jsonService).jsonToObject(Mockito.any(), Mockito.any(), Mockito.any());
        alarmTask.dealSingleAlarm(alarm, rule, "zh");
        rule.setMode(1);
        alarmTask.dealSingleAlarm(alarm, rule, "zh");
        Mockito.doReturn(true).when(malfunctionRepository).findByRelateOrderId(Mockito.any(), Mockito.any());
        rule.setMiniAlarmDuration("1:10");
        alarm.setAlarmraisedtime(1664522903896L);
        alarm.setAlarmclearedtime(1664522903896L);
        alarmTask.dealSingleAlarm(alarm, rule, "zh");
        alarmTask.dealSingleAlarm(alarm, rule, "en");
        rule.setTimeLimit("1:1");
        Mockito.doReturn(Collections.singletonList(alarm)).when(alarmService).getHistoryAlarm(Mockito.any(), Mockito.any());
        alarmTask.dealSingleAlarm(alarm, rule, "zh");
        rule.setTimeLimit("0:0");
        alarmTask.dealSingleAlarm(alarm, rule, "zh");
        rule.setTimeLimit(" : ");
        boolean flag = true;
        try {
            alarmTask.dealSingleAlarm(alarm, rule, "zh");
        } catch (Exception e) {
            flag = false;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void matchMoTest() throws UedmException {
        Mockito.doReturn(Collections.singletonList("1")).when(jsonService).jsonToObject(Mockito.any(), Mockito.any(), Mockito.any());
        Boolean flag = alarmTask.matchMo("1", "1");
        Assert.assertTrue(flag);
    }
}
