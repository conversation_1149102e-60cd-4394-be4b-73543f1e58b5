package com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.model;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class MaintenanceTemplateEntityTest {

    @Test
    public void test() throws Exception {
        MaintenanceTemplateEntity maintenanceTemplate = new MaintenanceTemplateEntity();
        PojoTestUtil.TestForPojo(maintenanceTemplate.getClass());
        Assert.assertEquals(maintenanceTemplate.toString(),new MaintenanceTemplateEntity().toString());
    }
}
