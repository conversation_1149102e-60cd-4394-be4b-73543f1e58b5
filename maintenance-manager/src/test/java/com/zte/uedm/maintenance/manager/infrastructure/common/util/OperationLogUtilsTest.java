package com.zte.uedm.maintenance.manager.infrastructure.common.util;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.maintenance.manager.application.command.InspectPlanStatusCommand;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.model.MaintenanceItem;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetemplate.model.MaintenanceTemplate;
import com.zte.uedm.maintenance.manager.infrastructure.common.bean.NameVersionBean;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceitem.mapper.MaintenanceItemMapper;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceitem.po.MaintenanceItemPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.mapper.MaintenanceObjectMapper;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceobject.po.MaintenanceObjectPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenancetask.po.MaintenanceTaskPO;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.TaskRuleBean;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.TriggerBaseBean;
import com.zte.uedm.maintenance.manager.interfaces.web.dto.MaintenanceTaskDetailDto;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceResultVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class OperationLogUtilsTest {
    @InjectMocks
    private OperationLogUtils operationLogUtils;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private MsgSenderService msgSenderService;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private JsonService jsonService;
    @Mock
    private MaintenanceItemMapper maintenanceItemMapper;
    @Mock
    private MaintenanceObjectMapper maintenanceObjectMapper;

    @Before
    public void setUp(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void sendKafkaMsgTest(){
        String time = "aaa";
        operationLogUtils.sendKafkaMsg("aaa","aaa","aaa");
        Assert.assertEquals("aaa",time);
        Mockito.doThrow(new RuntimeException("aaa")).when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
        operationLogUtils.sendKafkaMsg("aaa","aaa","aaa");
        Assert.assertEquals("aaa",time);
        Mockito.doReturn("2022-06-02 14:20:20").when(dateTimeService).getCurrentTime();
        operationLogUtils.sendKafkaMsg("aaa","aaa","aaa","aaa","aaa");
        Assert.assertEquals("aaa",time);
    }

    @Test
    public void getDetailTest() throws UedmException {
        Mockito.doReturn("aa").when(jsonService).objectToJson(Mockito.any());
        String detail = operationLogUtils.getDetail("aaa", "aaa");
        Assert.assertNotNull(detail);

        Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
        String detail1 = operationLogUtils.getDetail("aaa", "aaa");
        Assert.assertNotNull(detail1);
    }

    @Test
    public void sendMaintenancePlanAddLogTest() {
        boolean success = true;
        try{
            MaintenancePlanEntity planEntity = new MaintenancePlanEntity();
            planEntity.setStatus(1);
            planEntity.setVersion(1);
            planEntity.setReminderMode(1);
            planEntity.setMaintenancePlanType(1);
            planEntity.setExecuteRule("aaa");
            planEntity.setName("aaa");
            planEntity.setCreator("aaa");

            TaskRuleBean taskRuleBean = new TaskRuleBean();
            taskRuleBean.setGrain("day");
            taskRuleBean.setFrequency(2);
            taskRuleBean.setConsumeTime(1);

            TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
            triggerBaseBean.setIndex(1);
            triggerBaseBean.setBeginTime("aa");
            triggerBaseBean.setMonthDayIndex(1);
            triggerBaseBean.setWeekDayIndex(1);
            triggerBaseBean.setMonthIndex(1);
            List<TriggerBaseBean> triggerBaseBeans = new ArrayList<>(Collections.singletonList(triggerBaseBean));
            taskRuleBean.setTaskDatetime(new ArrayList<>(Collections.singletonList(triggerBaseBean)));

            Mockito.doReturn(taskRuleBean).when(jsonService).jsonToObject("aaa",TaskRuleBean.class);
            Mockito.doReturn(triggerBaseBeans).when(jsonService).jsonToObject(Mockito.any(),Mockito.any(),Mockito.any());
            List<NameVersionBean> templateOld = new ArrayList<>();
            NameVersionBean nameVersionBean = new NameVersionBean();
            templateOld.add(nameVersionBean);
            operationLogUtils.sendMaintenancePlanAddLog(planEntity,templateOld,dateTimeService.getCurrentTime());
        }catch (Exception e){
            success = false;
        }

        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenancePlanCopyLogTest() {
        boolean success = true;
        try{
            operationLogUtils.sendMaintenancePlanCopyLog(new MaintenancePlanEntity(),new ArrayList<>(),new MaintenancePlanEntity(),new ArrayList<>(),dateTimeService.getCurrentTime());
        }catch (Exception e){
            success = false;
        }

        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenancePlanEditLogTest() {
        boolean success = true;
        List<NameVersionBean> old = new ArrayList<>();
        NameVersionBean nameVersionBean = new NameVersionBean();
        old.add(nameVersionBean);
        try{
            operationLogUtils.sendMaintenancePlanEditLog(new MaintenancePlanEntity(),old,new MaintenancePlanEntity(),old,dateTimeService.getCurrentTime());
        }catch (Exception e){
            success = false;
        }

        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenancePlanDeleteLogTest() {
        boolean success = true;
        try{
            operationLogUtils.sendMaintenancePlanDeleteLog(new MaintenancePlanEntity(),dateTimeService.getCurrentTime());
        }catch (Exception e){
            success = false;
        }

        Assert.assertTrue(success);
    }


    @Test
    public void sendMaintenanceTaskAutoGenerateLog(){
        boolean success = true;
        try{
            operationLogUtils.sendMaintenanceTaskAutoGenerateLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);

        try{
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendMaintenanceTaskAutoGenerateLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenanceTaskExpiredLog(){
        boolean success = true;
        try{
            operationLogUtils.sendMaintenanceTaskExpiredLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);

        try{
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendMaintenanceTaskExpiredLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenanceTaskNotExecuteLog(){
        boolean success = true;
        try{
            operationLogUtils.sendMaintenanceTaskNotExecuteLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);

        try{
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendMaintenanceTaskNotExecuteLog(Arrays.asList("aaa","bbb"),"aaa");
        }catch (Exception e){
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void sendMaintenanceTemplateDeleteLogTest()
    {
        Exception exception = null;
        try {
            Mockito.doReturn("111").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            operationLogUtils.sendMaintenanceTemplateDeleteLog("1111");
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceTemplateDeleteLog("1111");
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMaintenanceTaskCompensationLogTest()
    {
        Exception exception = null;
        try {
            Mockito.doReturn("111").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            operationLogUtils.sendMaintenanceTaskCompensationLog(new ArrayList<>(),"aaa");
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendMaintenanceTaskCompensationLog(new ArrayList<>(),"aaa");
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendTaskGenerateSmsLogTest()
    {
        boolean success = true;
        try {
            Mockito.doReturn("111").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            operationLogUtils.sendTaskGenerateSmsLog("new ArrayList<>()",false,"aaa");
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendTaskGenerateSmsLog("new ArrayList<>()",true,"aaa");
        } catch (Exception e) {
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void sendTaskGenerateEmailLogTest()
    {
        boolean success = true;
        try {
            Mockito.doReturn("111").when(jsonService).objectToJson(Mockito.any());
            Mockito.doNothing().when(msgSenderService).sendMsgAsync(Mockito.any(),Mockito.any());
            operationLogUtils.sendTaskGenerateEmailLog("",true,"aaa");
            Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).objectToJson(Mockito.any());
            operationLogUtils.sendTaskGenerateEmailLog("",true,"aaa");
        } catch (Exception e) {
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void lastTest() throws UedmException {
        MaintenancePlanEntity old = new MaintenancePlanEntity();
        old.setStatus(1);
        old.setVersion(1);
        old.setReminderMode(1);
        old.setMaintenancePlanType(1);
        old.setExecuteRule("aaa");
        old.setName("aaa");
        old.setCreator("aaa");

        TaskRuleBean taskRuleBean = new TaskRuleBean();
        taskRuleBean.setGrain("day");
        taskRuleBean.setFrequency(2);
        taskRuleBean.setConsumeTime(1);

        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(1);
        triggerBaseBean.setBeginTime("aa");
        triggerBaseBean.setMonthDayIndex(1);
        triggerBaseBean.setWeekDayIndex(1);
        triggerBaseBean.setMonthIndex(1);
        List<TriggerBaseBean> triggerBaseBeans = new ArrayList<>(Collections.singletonList(triggerBaseBean));
        taskRuleBean.setTaskDatetime(new ArrayList<>(Collections.singletonList(triggerBaseBean)));

        Mockito.doReturn(taskRuleBean).when(jsonService).jsonToObject("aaa",TaskRuleBean.class);
        Mockito.doReturn(triggerBaseBeans).when(jsonService).jsonToObject(Mockito.any(),Mockito.any(),Mockito.any());

        List<NameVersionBean> templateOld = new ArrayList<>();
        NameVersionBean nameVersionBean = new NameVersionBean();
        templateOld.add(nameVersionBean);

        String planDetail = operationLogUtils.getMaintenancePlanDetail(old, templateOld, old, templateOld);
        String planDetail1 = operationLogUtils.getMaintenancePlanDetail(old, templateOld);

        Assert.assertNotEquals("", planDetail);
        Assert.assertNotEquals("", planDetail1);
    }

    @Test
    public void lastTestOne(){
        MaintenancePlanEntity old = new MaintenancePlanEntity();
        old.setStatus(1);
        old.setVersion(1);
        old.setReminderMode(1);
        old.setMaintenancePlanType(1);

        List<NameVersionBean> templateOld = new ArrayList<>();
        NameVersionBean nameVersionBean = new NameVersionBean();
        templateOld.add(nameVersionBean);

        Mockito.doThrow(new RuntimeException("aaa")).when(i18nUtils).getMapFieldByLanguageOption(Mockito.any(),Mockito.any());

        String planDetail = operationLogUtils.getMaintenancePlanDetail(old, templateOld, old, templateOld);
        String planDetail1 = operationLogUtils.getMaintenancePlanDetail(old, templateOld);

        Assert.assertNotEquals("", planDetail);
        Assert.assertNotEquals("", planDetail1);
    }

    @Test
    public void sendExecuteTaskLogTest1(){
        Exception e=null;
        try {
            MaintenanceTaskPO maintenanceTaskPO=new MaintenanceTaskPO();
            MaintenanceResultVO resultVOS = new MaintenanceResultVO();
            maintenanceTaskPO.setExecutor("aaa");
            maintenanceTaskPO.setConclusion(1);
            maintenanceTaskPO.setName("aaa");
            maintenanceTaskPO.setStatus(1);
            resultVOS.setConclusion(1);
            resultVOS.setStatus(1);
            resultVOS.setMaintenanceItemId("aaa");
            resultVOS.setMaintenanceObjId("aaa");
            MaintenanceItemPO maintenanceItemPO1 = new MaintenanceItemPO();
            maintenanceItemPO1.setId("aaa");
            maintenanceItemPO1.setName("bbb");
            List<MaintenanceItemPO> list = new ArrayList<>();
            list.add(maintenanceItemPO1);
            Mockito.doReturn(list).when(maintenanceItemMapper).itemList(Mockito.any());
            MaintenanceObjectPO maintenanceObjectPO1 = new MaintenanceObjectPO();
            maintenanceObjectPO1.setId("aaa");
            maintenanceObjectPO1.setInstanceName("bbb");
            List<MaintenanceObjectPO> list1 = new ArrayList<>();
            list1.add(maintenanceObjectPO1);
            Mockito.doReturn(list1).when(maintenanceObjectMapper).objList(Mockito.any());
            operationLogUtils.sendExecuteTaskLog(maintenanceTaskPO,Collections.singletonList(resultVOS));
        }catch (Exception exception){
            e=exception;
        }
        Assert.assertNull(e);
    }


    @Test
    public void sendMaintenancePlanStatusLogTest(){
        Exception e=null;
        try {
            InspectPlanStatusCommand inspectPlanStatusCommand=new InspectPlanStatusCommand();
            inspectPlanStatusCommand.setNewStatus(1);
            inspectPlanStatusCommand.setNewStatus(1);
            inspectPlanStatusCommand.setName("aaa");
            operationLogUtils.sendMaintenancePlanStatusLog(inspectPlanStatusCommand);
        }catch (Exception exception){
            e=exception;
        }
        Assert.assertNull(e);
    }

    @Test
    public void sendMaintenanceTemplateAddLogTest(){
        Exception exception=null;
        try {
            MaintenanceTemplate template=new MaintenanceTemplate();
            template.setBaseVersionId("baseId");
            template.setMaintenanceObjType("objType");
            template.setName("name");
            template.setVersion(1);
            template.setId("111");
            MaintenanceItem item=new MaintenanceItem();
            item.setName("itemName");
            item.setAttachment("attachment");
            item.setContent("content");
            template.setItemList(Collections.singletonList(item));
            operationLogUtils.sendMaintenanceTemplateAddLog(template);
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceTemplateAddLog(template);
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMaintenanceTemplateCopyLogTest(){
        Exception exception=null;
        try {
            MaintenanceTemplate template=new MaintenanceTemplate();
            template.setBaseVersionId("baseId");
            template.setMaintenanceObjType("objType");
            template.setName("name");
            template.setVersion(1);
            template.setId("111");
            MaintenanceItem item=new MaintenanceItem();
            item.setName("itemName");
            item.setAttachment("attachment");
            item.setContent("content");
            template.setItemList(Collections.singletonList(item));
            operationLogUtils.sendMaintenanceTemplateCopyLog(template);
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceTemplateCopyLog(template);
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMaintenanceTemplateEditLog2Test(){
        Exception exception=null;
        try {
            MaintenanceTemplate template=new MaintenanceTemplate();
            template.setBaseVersionId("baseId");
            template.setMaintenanceObjType("objType");
            template.setName("name");
            template.setVersion(1);
            template.setId("111");
            MaintenanceItem item=new MaintenanceItem();
            item.setName("itemName");
            item.setAttachment("attachment");
            item.setContent("content");
            template.setItemList(Collections.singletonList(item));
            operationLogUtils.sendMaintenanceTemplateEditLog(template,template);
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceTemplateEditLog(template,template);
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMaintenanceTemplateUploadLogTest(){
        Exception exception=null;
        try {
            MaintenanceTemplate template=new MaintenanceTemplate();
            template.setBaseVersionId("baseId");
            template.setMaintenanceObjType("objType");
            template.setName("name");
            template.setVersion(1);
            template.setId("111");
            MaintenanceItem item=new MaintenanceItem();
            item.setName("itemName");
            item.setAttachment("attachment");
            item.setContent("content");
            template.setItemList(Collections.singletonList(item));
            operationLogUtils.sendMaintenanceTemplateUploadLog(Collections.singletonList(template));
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceTemplateUploadLog(Collections.singletonList(template));
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }
    @Test
    public void sendMaintenancePreviewLogTest(){
        Exception exception=null;
        try {
            MaintenanceTaskDetailDto reportVo=new MaintenanceTaskDetailDto();
            MaintenanceTaskDetailDto.PlanInfo planInfo = new MaintenanceTaskDetailDto.PlanInfo();
            planInfo.setName("test");
            planInfo.setPlanBeginTime("2020-10-20");
            planInfo.setPlanEndTime("2020-10-20");
            reportVo.setPlanInfo(planInfo);
            MaintenanceTaskDetailDto.TaskInfo taskInfo = new MaintenanceTaskDetailDto.TaskInfo();
            taskInfo.setName("test");
            taskInfo.setTaskBeginTime("2020-10-20");
            taskInfo.setTaskEndTime("2020-10-20");
            reportVo.setTaskInfo(taskInfo);
            operationLogUtils.sendMaintenancePreviewLog(reportVo,"2022-10-10 10:00:00");
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenancePreviewLog(reportVo,"2022-10-10 10:00:00");
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMaintenanceDownloadLogTest(){
        Exception exception=null;
        try {
            MaintenanceTaskDetailDto reportVo=new MaintenanceTaskDetailDto();
            MaintenanceTaskDetailDto.PlanInfo planInfo = new MaintenanceTaskDetailDto.PlanInfo();
            planInfo.setName("test");
            planInfo.setPlanBeginTime("2020-10-20");
            planInfo.setPlanEndTime("2020-10-20");
            reportVo.setPlanInfo(planInfo);
            MaintenanceTaskDetailDto.TaskInfo taskInfo = new MaintenanceTaskDetailDto.TaskInfo();
            taskInfo.setName("test");
            taskInfo.setTaskBeginTime("2020-10-20");
            taskInfo.setTaskEndTime("2020-10-20");
            reportVo.setTaskInfo(taskInfo);
            operationLogUtils.sendMaintenanceDownloadLog(reportVo,1,"2022-10-10 10:00:00");
            Mockito.doThrow(new RuntimeException("aaa")).when(dateTimeService).getCurrentTime();
            operationLogUtils.sendMaintenanceDownloadLog(reportVo,1,"2022-10-10 10:00:00");
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }
}
