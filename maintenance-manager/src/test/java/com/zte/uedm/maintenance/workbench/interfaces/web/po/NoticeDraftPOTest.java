package com.zte.uedm.maintenance.workbench.interfaces.web.po;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class NoticeDraftPOTest {

    @Test
    public void test() throws Exception {
        NoticeDraftPO noticeDraftPO = new NoticeDraftPO();
        PojoTestUtil.TestForPojo(noticeDraftPO.getClass());
        noticeDraftPO.setId("xxx");
        Assert.assertNotNull(noticeDraftPO.toString());
        Assert.assertEquals("xxx",  noticeDraftPO.getId());
    }
}
