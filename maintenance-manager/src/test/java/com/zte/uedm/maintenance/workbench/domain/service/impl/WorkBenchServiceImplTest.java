package com.zte.uedm.maintenance.workbench.domain.service.impl;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.malfunction.application.executor.impl.MalfunctionQueryServiceImpl;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionDetailDto;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionDetailListDto;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionListDto;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionListOfMineDto;
import com.zte.uedm.maintenance.manager.application.executor.impl.MaintenanceTaskQueryServiceImpl;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.UserNameUtils;
import com.zte.uedm.maintenance.manager.interfaces.web.dto.MaintenanceTaskListDTO;
import com.zte.uedm.maintenance.manager.interfaces.web.dto.MaintenanceTaskListOfMineDTO;
import com.zte.uedm.maintenance.risk.application.executor.impl.RiskQueryServiceImpl;
import com.zte.uedm.maintenance.workbench.domain.service.FlowStatisticsService;
import com.zte.uedm.maintenance.workbench.infrastructure.client.inspect.impl.AssetsRpcImpl;
import com.zte.uedm.maintenance.workbench.infrastructure.client.inspect.impl.InspectRpcImpl;
import com.zte.uedm.maintenance.workbench.infrastructure.repository.notice.mapper.StatisticsFlowDataMapper;
import com.zte.uedm.maintenance.workbench.infrastructure.repository.notice.mapper.UserTemplateMapper;
import com.zte.uedm.maintenance.workbench.interfaces.web.bean.StatisticsFlowDataBean;
import com.zte.uedm.maintenance.workbench.interfaces.web.bean.WorkBenchControllerSaveTemplateRequestBean;
import com.zte.uedm.maintenance.workbench.interfaces.web.dto.*;
import com.zte.uedm.maintenance.workbench.interfaces.web.vo.StatisticsFlowDataQueryVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;

import static com.zte.uedm.maintenance.manager.infrastructure.common.GlobalConst.*;
import static org.junit.Assert.assertEquals;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(LoginHelper.class)
public class WorkBenchServiceImplTest {
    @InjectMocks
    private WorkBenchServiceImpl workBenchServiceImpl;

    @Mock
    UserTemplateMapper userTemplateMapper;

    @Mock
    StatisticsFlowDataMapper statisticsFlowDataMapper;

    @Mock
    private RiskQueryServiceImpl riskQueryService;

    @Mock
    private MalfunctionQueryServiceImpl malfunctionQueryService;

    @Mock
    private AssetsRpcImpl assetsRpc;

    @Mock
    private InspectRpcImpl inspectRpc;

    @Mock
    private MaintenanceTaskQueryServiceImpl maintenanceTaskQueryService;

    @Mock
    private UserNameUtils userNameUtils;

    @Mock
    private I18nUtils i18nUtils;

    @Mock
    private FlowStatisticsService flowStatisticsService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(userNameUtils.getSingleFullName(Mockito.any())).thenReturn("admin");
    }


    @Test
    public void testDemon() throws UedmException {
        String result = workBenchServiceImpl.demon();
        Assert.assertEquals(result, null);
    }

    @Test
    public void saveTemplateExistSuccess() {
        WorkBenchControllerSaveTemplateRequestBean aa = new WorkBenchControllerSaveTemplateRequestBean();
        aa.setUserName("11");
        aa.setTemplateName("11");
        aa.setCreator("11");
        aa.setQueryCondKey("11");
        aa.setType(11);

        WorkBenchControllerSaveTemplateRequestBean isExistBean = new WorkBenchControllerSaveTemplateRequestBean();
        isExistBean.setType(11);
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Mockito.when(userTemplateMapper.updateUserFollowTemplate(Mockito.any())).thenReturn(1);
        Assert.assertEquals(workBenchServiceImpl.saveTemplate(aa).getMessage(),"save user follow template success");

    }
    @Test
    public void saveTemplateExistFailed() {
        WorkBenchControllerSaveTemplateRequestBean aa = new WorkBenchControllerSaveTemplateRequestBean();
        aa.setUserName("11");
        aa.setTemplateName("11");
        aa.setCreator("11");
        aa.setQueryCondKey("11");
        aa.setType(11);

        WorkBenchControllerSaveTemplateRequestBean isExistBean = new WorkBenchControllerSaveTemplateRequestBean();
        isExistBean.setType(11);
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Mockito.when(userTemplateMapper.updateUserFollowTemplate(Mockito.any())).thenReturn(0);

        Assert.assertEquals(workBenchServiceImpl.saveTemplate(aa).getMessage(),"insert/update user follow template failed!");

    }
    @Test
    public void saveTemplateNotExistFailed() {
        WorkBenchControllerSaveTemplateRequestBean aa = new WorkBenchControllerSaveTemplateRequestBean();
        aa.setUserName("11");
        aa.setTemplateName("11");
        aa.setCreator("11");
        aa.setQueryCondKey("11");
        aa.setType(11);

        WorkBenchControllerSaveTemplateRequestBean isExistBean = null;
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Mockito.when(userTemplateMapper.addUserFollowTemplate(Mockito.any())).thenReturn(0);

        Assert.assertEquals(workBenchServiceImpl.saveTemplate(aa).getMessage(),"insert/update user follow template failed!");

    }
    @Test
    public void saveTemplateNotExistSuccess() {
        WorkBenchControllerSaveTemplateRequestBean aa = new WorkBenchControllerSaveTemplateRequestBean();
        aa.setUserName("11");
        aa.setTemplateName("11");
        aa.setCreator("11");
        aa.setQueryCondKey("11");
        aa.setType(11);

        WorkBenchControllerSaveTemplateRequestBean isExistBean = null;
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Mockito.when(userTemplateMapper.addUserFollowTemplate(Mockito.any())).thenReturn(1);

        Assert.assertEquals(workBenchServiceImpl.saveTemplate(aa).getMessage(),"save user follow template success");

    }

    @Test
    public void addStatisticsFlowDataTest() {
        Mockito.when(statisticsFlowDataMapper.insert(Mockito.any())).thenReturn(1);
        workBenchServiceImpl.addStatisticsFlowData(new ArrayList<>());
        Assert.assertNotNull("");
    }

    @Test
    public void getStatisticsFlowDataTest() {
        List<StatisticsFlowDataBean> list = new ArrayList<>();
        StatisticsFlowDataBean statisticsFlowDataBean = new StatisticsFlowDataBean();
        statisticsFlowDataBean.setTime("2022-12-01");
        statisticsFlowDataBean.setChildType("risk");
        statisticsFlowDataBean.setValue("2");
        list.add(statisticsFlowDataBean);
        StatisticsFlowDataBean statisticsFlowDataBean2 = new StatisticsFlowDataBean();
        statisticsFlowDataBean2.setChildType("malfunction");
        list.add(statisticsFlowDataBean2);
        StatisticsFlowDataBean statisticsFlowDataBean3 = new StatisticsFlowDataBean();
        statisticsFlowDataBean3.setChildType("maintenance");
        list.add(statisticsFlowDataBean3);
        StatisticsFlowDataBean statisticsFlowDataBean4 = new StatisticsFlowDataBean();
        statisticsFlowDataBean4.setChildType("inspect");
        list.add(statisticsFlowDataBean4);
        StatisticsFlowDataBean statisticsFlowDataBean5 = new StatisticsFlowDataBean();
        statisticsFlowDataBean5.setChildType("assets");
        list.add(statisticsFlowDataBean5);
        StatisticsFlowDataBean statisticsFlowDataBean6 = new StatisticsFlowDataBean();
        statisticsFlowDataBean6.setChildType("add");
        list.add(statisticsFlowDataBean6);
        StatisticsFlowDataBean statisticsFlowDataBean7 = new StatisticsFlowDataBean();
        statisticsFlowDataBean7.setChildType("close");
        list.add(statisticsFlowDataBean7);
        StatisticsFlowDataBean statisticsFlowDataBean8 = new StatisticsFlowDataBean();
        statisticsFlowDataBean8.setChildType("un_solved");
        list.add(statisticsFlowDataBean8);
        StatisticsFlowDataBean statisticsFlowDataBean9 = new StatisticsFlowDataBean();
        statisticsFlowDataBean9.setChildType("normal");
        list.add(statisticsFlowDataBean9);
        StatisticsFlowDataBean statisticsFlowDataBean0 = new StatisticsFlowDataBean();
        statisticsFlowDataBean0.setChildType("delay_unsolved");
        list.add(statisticsFlowDataBean0);
        Mockito.when(statisticsFlowDataMapper.getData(Mockito.any())).thenReturn(list);
        Mockito.when(flowStatisticsService.statisticsFlowData(Mockito.any(), Mockito.anyBoolean())).thenReturn(list);
        StatisticsFlowDataQueryVo statisticsFlowDataQueryVo = new StatisticsFlowDataQueryVo();
        statisticsFlowDataQueryVo.setStartTime("2022-12-01 00:00:00");
        statisticsFlowDataQueryVo.setEndTime("2022-12-31 23:59:59");
        statisticsFlowDataQueryVo.setPersonal(true);
        statisticsFlowDataQueryVo.setStatisticsType("month");
        workBenchServiceImpl.getStatisticsFlowData(statisticsFlowDataQueryVo);
        statisticsFlowDataQueryVo.setPersonal(false);
        statisticsFlowDataQueryVo.setStatisticsType("year");
        workBenchServiceImpl.getStatisticsFlowData(statisticsFlowDataQueryVo);
        statisticsFlowDataQueryVo.setPersonal(false);
        statisticsFlowDataQueryVo.setStatisticsType("month");
        workBenchServiceImpl.getStatisticsFlowData(statisticsFlowDataQueryVo);
        Assert.assertNotNull("");
    }

    @Test
    public void getTemplateByUserNull() {
        WorkBenchControllerSaveTemplateRequestBean isExistBean = null;
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Assert.assertEquals(workBenchServiceImpl.getTemplateByUser().getMessage(),"current user do not follow any template.");
    }
    @Test
    public void getTemplateByUserNotNull() {
        WorkBenchControllerSaveTemplateRequestBean isExistBean = new WorkBenchControllerSaveTemplateRequestBean();
        isExistBean.setCreator("11");
        Mockito.when(userTemplateMapper.selectUserFollowTemplate(Mockito.any())).thenReturn(isExistBean);
        Assert.assertEquals(workBenchServiceImpl.getTemplateByUser().getMessage(),"success");
    }


    @Test
    public void getMyProcessListTest() throws Exception {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("risk");
        processQuery.setSource(source);

        ThreadPoolExecutor mock = PowerMockito.mock(ThreadPoolExecutor.class);
        Whitebox.setInternalState(workBenchServiceImpl, "workbenchPool", mock);
        MalfunctionListOfMineDto malfunctionListOfMineDto = new MalfunctionListOfMineDto();
        Map<String, List<MalfunctionDetailListDto>> riskMap = new HashMap<>();
        List<MalfunctionDetailListDto> initList = new ArrayList<>();
        List<MalfunctionDetailListDto> tobeList = new ArrayList<>();
        List<MalfunctionDetailListDto> doneList = new ArrayList<>();

        List<MalfunctionDetailDto.DealRecord> dealRecordList = new ArrayList<>();
        MalfunctionDetailDto.DealRecord record = new MalfunctionDetailDto.DealRecord();
        record.setDealStatus("取消");
        record.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record1 = new MalfunctionDetailDto.DealRecord();
        record1.setDealStatus("提交");
        record1.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record2 = new MalfunctionDetailDto.DealRecord();
        record2.setDealStatus("完结");
        record2.setDealEndTime("2022-12-12 14:11:10");
        dealRecordList.add(record);
        dealRecordList.add(record1);
        dealRecordList.add(record2);

        MalfunctionListDto malfunctionListDto = new MalfunctionListDto();
        malfunctionListDto.setId("abc");
        malfunctionListDto.setMalfunctionName("test");
        malfunctionListDto.setWorkflowId("wf");
        malfunctionListDto.setHandler("handler");
        malfunctionListDto.setStatus(1);

        MalfunctionListDto malfunctionListDto1 = new MalfunctionListDto();
        malfunctionListDto1.setId("abc");
        malfunctionListDto1.setMalfunctionName("test");
        malfunctionListDto1.setWorkflowId("wf");
        malfunctionListDto1.setHandler("handler");
        malfunctionListDto1.setStatus(0);

        MalfunctionListDto malfunctionListDto2 = new MalfunctionListDto();
        malfunctionListDto2.setId("abc");
        malfunctionListDto2.setMalfunctionName("test");
        malfunctionListDto2.setWorkflowId("wf");
        malfunctionListDto2.setHandler("handler");
        malfunctionListDto2.setStatus(2);

        MalfunctionListDto malfunctionListDto3 = new MalfunctionListDto();
        malfunctionListDto3.setId("abc");
        malfunctionListDto3.setMalfunctionName("test");
        malfunctionListDto3.setWorkflowId("wf");
        malfunctionListDto3.setHandler("handler");
        malfunctionListDto3.setStatus(3);

        MalfunctionListDto malfunctionListDto4 = new MalfunctionListDto();
        malfunctionListDto4.setId("abc");
        malfunctionListDto4.setMalfunctionName("test");
        malfunctionListDto4.setWorkflowId("wf");
        malfunctionListDto4.setHandler("handler");
        malfunctionListDto4.setStatus(4);

        MalfunctionListDto malfunctionListDto5 = new MalfunctionListDto();
        malfunctionListDto4.setId("abc");
        malfunctionListDto4.setMalfunctionName("test");
        malfunctionListDto4.setWorkflowId("wf");
        malfunctionListDto4.setHandler("handler");
        malfunctionListDto4.setStatus(5);

        MalfunctionDetailListDto item = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item1 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item2 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item3 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item4 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item5 = new MalfunctionDetailListDto();

        item.setDealRecord(dealRecordList);
        item.setMalfunctionListDto(malfunctionListDto);
        item1.setDealRecord(dealRecordList);
        item1.setMalfunctionListDto(malfunctionListDto1);
        item2.setDealRecord(dealRecordList);
        item2.setMalfunctionListDto(malfunctionListDto2);
        item3.setDealRecord(dealRecordList);
        item3.setMalfunctionListDto(malfunctionListDto3);
        item4.setDealRecord(dealRecordList);
        item4.setMalfunctionListDto(malfunctionListDto4);
        item5.setDealRecord(dealRecordList);
        item5.setMalfunctionListDto(malfunctionListDto5);
        initList.add(item);
        initList.add(item1);
        initList.add(item2);
        initList.add(item3);
        initList.add(item4);
        initList.add(item5);
        tobeList.add(item);
        tobeList.add(item1);
        tobeList.add(item2);
        tobeList.add(item3);
        tobeList.add(item4);
        tobeList.add(item5);
        doneList.add(item);
        doneList.add(item1);
        doneList.add(item2);
        doneList.add(item3);
        doneList.add(item4);
        doneList.add(item5);
        riskMap.put("Initiated",initList);
        riskMap.put("ToBeHandled",tobeList);
        riskMap.put("Handled",doneList);
        malfunctionListOfMineDto.setMalfunctionListOfMine(riskMap);

        when(riskQueryService.queryRiskListOfMine(Mockito.any(),Mockito.any())).thenReturn(malfunctionListOfMineDto);

        processQuery.setSort("source");
        processQuery.setDirection("asc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");

        processQuery.setSort("source");
        processQuery.setDirection("desc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");
        processQuery.setSort("processTime");
        processQuery.setDirection("asc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");
        processQuery.setSort("processTime");
        processQuery.setDirection("desc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");
        processQuery.setSort("acceptanceTime");
        processQuery.setDirection("asc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");
        processQuery.setSort("acceptanceTime");
        processQuery.setDirection("desc");
        workBenchServiceImpl.getMyProcessList(processQuery,"admin","123",0,"en-US");
        ProcessQueryVO processQuery1 = new ProcessQueryVO();
        List<String> source11 = new ArrayList<>();
        processQuery1.setSource(source11);
        workBenchServiceImpl.getMyProcessList(processQuery1,"admin","123",0,"en-US");
        Assert.assertNotNull("riskFormData1");
    }

    @Test
    public void getMyProcessRiskTest() throws UedmException {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("risk");
        processQuery.setSource(source);

        ThreadPoolExecutor mock = PowerMockito.mock(ThreadPoolExecutor.class);
        Whitebox.setInternalState(workBenchServiceImpl, "workbenchPool", mock);
        MalfunctionListOfMineDto malfunctionListOfMineDto = new MalfunctionListOfMineDto();
        Map<String, List<MalfunctionDetailListDto>> riskMap = new HashMap<>();
        List<MalfunctionDetailListDto> initList = new ArrayList<>();
        List<MalfunctionDetailListDto> tobeList = new ArrayList<>();
        List<MalfunctionDetailListDto> doneList = new ArrayList<>();

        List<MalfunctionDetailDto.DealRecord> dealRecordList = new ArrayList<>();
        MalfunctionDetailDto.DealRecord record = new MalfunctionDetailDto.DealRecord();
        record.setDealStatus("取消");
        record.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record1 = new MalfunctionDetailDto.DealRecord();
        record1.setDealStatus("提交");
        record1.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record2 = new MalfunctionDetailDto.DealRecord();
        record2.setDealStatus("完结");
        record2.setDealEndTime("2022-12-12 14:11:10");
        dealRecordList.add(record);
        dealRecordList.add(record1);
        dealRecordList.add(record2);

        MalfunctionListDto malfunctionListDto = new MalfunctionListDto();
        malfunctionListDto.setId("abc");
        malfunctionListDto.setMalfunctionName("test");
        malfunctionListDto.setWorkflowId("wf");
        malfunctionListDto.setHandler("handler");
        malfunctionListDto.setStatus(1);
        malfunctionListDto.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto1 = new MalfunctionListDto();
        malfunctionListDto1.setId("abc");
        malfunctionListDto1.setMalfunctionName("test");
        malfunctionListDto1.setWorkflowId("wf");
        malfunctionListDto1.setHandler("handler");
        malfunctionListDto1.setStatus(0);
        malfunctionListDto1.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto2 = new MalfunctionListDto();
        malfunctionListDto2.setId("abc");
        malfunctionListDto2.setMalfunctionName("test");
        malfunctionListDto2.setWorkflowId("wf");
        malfunctionListDto2.setHandler("handler");
        malfunctionListDto2.setStatus(2);
        malfunctionListDto2.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto3 = new MalfunctionListDto();
        malfunctionListDto3.setId("abc");
        malfunctionListDto3.setMalfunctionName("test");
        malfunctionListDto3.setWorkflowId("wf");
        malfunctionListDto3.setHandler("handler");
        malfunctionListDto3.setStatus(3);
        malfunctionListDto3.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto4 = new MalfunctionListDto();
        malfunctionListDto4.setId("abc");
        malfunctionListDto4.setMalfunctionName("test");
        malfunctionListDto4.setWorkflowId("wf");
        malfunctionListDto4.setHandler("handler");
        malfunctionListDto4.setStatus(4);
        malfunctionListDto4.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto5 = new MalfunctionListDto();
        malfunctionListDto5.setId("abc");
        malfunctionListDto5.setMalfunctionName("test");
        malfunctionListDto5.setWorkflowId("wf");
        malfunctionListDto5.setHandler("handler");
        malfunctionListDto5.setStatus(5);
        malfunctionListDto5.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionDetailListDto item = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item1 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item2 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item3 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item4 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item5 = new MalfunctionDetailListDto();

        item.setDealRecord(dealRecordList);
        item.setMalfunctionListDto(malfunctionListDto);
        item1.setDealRecord(dealRecordList);
        item1.setMalfunctionListDto(malfunctionListDto1);
        item2.setDealRecord(dealRecordList);
        item2.setMalfunctionListDto(malfunctionListDto2);
        item3.setDealRecord(dealRecordList);
        item3.setMalfunctionListDto(malfunctionListDto3);
        item4.setDealRecord(dealRecordList);
        item4.setMalfunctionListDto(malfunctionListDto4);
        item5.setDealRecord(dealRecordList);
        item5.setMalfunctionListDto(malfunctionListDto5);
        initList.add(item);
        initList.add(item1);
        initList.add(item2);
        initList.add(item3);
        initList.add(item4);
        initList.add(item5);
        tobeList.add(item);
        tobeList.add(item1);
        tobeList.add(item2);
        tobeList.add(item3);
        tobeList.add(item4);
        tobeList.add(item5);
        doneList.add(item);
        doneList.add(item1);
        doneList.add(item2);
        doneList.add(item3);
        doneList.add(item4);
        doneList.add(item5);
        riskMap.put("Initiated",initList);
        riskMap.put("ToBeHandled",tobeList);
        riskMap.put("Handled",doneList);
        malfunctionListOfMineDto.setMalfunctionListOfMine(riskMap);

        when(riskQueryService.queryRiskListOfMine(Mockito.any(),Mockito.any())).thenReturn(malfunctionListOfMineDto);

        List<MyProcess> processList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(5);
        when(i18nUtils.getMapFieldByLanguageOption(Mockito.any(),Mockito.any())).thenReturn("risk");
        workBenchServiceImpl.queryRisk(processList,processQuery,countDownLatch,"admin","en-US");
        processQuery.setProcessTimeTo("2099-1-1 10:10:10");
        processQuery.setProcessTimeFrom("1990-1-1 10:10:10");
        processQuery.setAcceptanceTimeTo("2099-1-1 10:10:10");
        processQuery.setAcceptanceTimeFrom("1990-1-1 10:10:10");
        processQuery.setTitle("e");
        workBenchServiceImpl.queryRisk(processList,processQuery,countDownLatch,"admin","en-US");

        Assert.assertNotNull("riskFormData1");
    }

    @Test
    public void getMyProcessMalTest() throws UedmException {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("malfunction");
        processQuery.setSource(source);

        ThreadPoolExecutor mock = PowerMockito.mock(ThreadPoolExecutor.class);
        Whitebox.setInternalState(workBenchServiceImpl, "workbenchPool", mock);
        MalfunctionListOfMineDto malfunctionListOfMineDto = new MalfunctionListOfMineDto();
        Map<String, List<MalfunctionDetailListDto>> riskMap = new HashMap<>();
        List<MalfunctionDetailListDto> initList = new ArrayList<>();
        List<MalfunctionDetailListDto> tobeList = new ArrayList<>();
        List<MalfunctionDetailListDto> doneList = new ArrayList<>();

        List<MalfunctionDetailDto.DealRecord> dealRecordList = new ArrayList<>();
        MalfunctionDetailDto.DealRecord record = new MalfunctionDetailDto.DealRecord();
        record.setDealStatus("取消");
        record.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record1 = new MalfunctionDetailDto.DealRecord();
        record1.setDealStatus("提交");
        record1.setDealEndTime("2022-12-12 14:11:10");
        MalfunctionDetailDto.DealRecord record2 = new MalfunctionDetailDto.DealRecord();
        record2.setDealStatus("完结");
        record2.setDealEndTime("2022-12-12 14:11:10");
        dealRecordList.add(record);
        dealRecordList.add(record1);
        dealRecordList.add(record2);

        MalfunctionListDto malfunctionListDto = new MalfunctionListDto();
        malfunctionListDto.setId("abc");
        malfunctionListDto.setMalfunctionName("test");
        malfunctionListDto.setWorkflowId("wf");
        malfunctionListDto.setHandler("handler");
        malfunctionListDto.setStatus(1);
        malfunctionListDto.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto1 = new MalfunctionListDto();
        malfunctionListDto1.setId("abc");
        malfunctionListDto1.setMalfunctionName("test");
        malfunctionListDto1.setWorkflowId("wf");
        malfunctionListDto1.setHandler("handler");
        malfunctionListDto1.setStatus(0);
        malfunctionListDto1.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto2 = new MalfunctionListDto();
        malfunctionListDto2.setId("abc");
        malfunctionListDto2.setMalfunctionName("test");
        malfunctionListDto2.setWorkflowId("wf");
        malfunctionListDto2.setHandler("handler");
        malfunctionListDto2.setStatus(2);
        malfunctionListDto2.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto3 = new MalfunctionListDto();
        malfunctionListDto3.setId("abc");
        malfunctionListDto3.setMalfunctionName("test");
        malfunctionListDto3.setWorkflowId("wf");
        malfunctionListDto3.setHandler("handler");
        malfunctionListDto3.setStatus(3);
        malfunctionListDto3.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto4 = new MalfunctionListDto();
        malfunctionListDto4.setId("abc");
        malfunctionListDto4.setMalfunctionName("test");
        malfunctionListDto4.setWorkflowId("wf");
        malfunctionListDto4.setHandler("handler");
        malfunctionListDto4.setStatus(4);
        malfunctionListDto4.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionListDto malfunctionListDto5 = new MalfunctionListDto();
        malfunctionListDto5.setId("abc");
        malfunctionListDto5.setMalfunctionName("test");
        malfunctionListDto5.setWorkflowId("wf");
        malfunctionListDto5.setHandler("handler");
        malfunctionListDto5.setStatus(5);
        malfunctionListDto5.setGmtCreate("2022-10-12 10:10:10");

        MalfunctionDetailListDto item = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item1 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item2 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item3 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item4 = new MalfunctionDetailListDto();
        MalfunctionDetailListDto item5 = new MalfunctionDetailListDto();

        item.setDealRecord(dealRecordList);
        item.setMalfunctionListDto(malfunctionListDto);
        item1.setDealRecord(dealRecordList);
        item1.setMalfunctionListDto(malfunctionListDto1);
        item2.setDealRecord(dealRecordList);
        item2.setMalfunctionListDto(malfunctionListDto2);
        item3.setDealRecord(dealRecordList);
        item3.setMalfunctionListDto(malfunctionListDto3);
        item4.setDealRecord(dealRecordList);
        item4.setMalfunctionListDto(malfunctionListDto4);
        item5.setDealRecord(dealRecordList);
        item5.setMalfunctionListDto(malfunctionListDto5);
        initList.add(item);
        initList.add(item1);
        initList.add(item2);
        initList.add(item3);
        initList.add(item4);
        initList.add(item5);
        tobeList.add(item);
        tobeList.add(item1);
        tobeList.add(item2);
        tobeList.add(item3);
        tobeList.add(item4);
        tobeList.add(item5);
        doneList.add(item);
        doneList.add(item1);
        doneList.add(item2);
        doneList.add(item3);
        doneList.add(item4);
        doneList.add(item5);
        riskMap.put("Initiated",initList);
        riskMap.put("ToBeHandled",tobeList);
        riskMap.put("Handled",doneList);
        malfunctionListOfMineDto.setMalfunctionListOfMine(riskMap);

        when(malfunctionQueryService.queryMalfunctionListOfMine(Mockito.any(),Mockito.any())).thenReturn(malfunctionListOfMineDto);

        List<MyProcess> processList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(5);
        when(i18nUtils.getMapFieldByLanguageOption(Mockito.any(),Mockito.any())).thenReturn("risk");
        workBenchServiceImpl.queryMalfunction(processList,processQuery,countDownLatch,"admin","en-US");
        processQuery.setProcessTimeTo("2099-1-1 10:10:10");
        processQuery.setProcessTimeFrom("1990-1-1 10:10:10");
        processQuery.setAcceptanceTimeTo("2099-1-1 10:10:10");
        processQuery.setAcceptanceTimeFrom("1990-1-1 10:10:10");
        processQuery.setTitle("e");
        workBenchServiceImpl.queryMalfunction(processList,processQuery,countDownLatch,"admin","en-US");

        Assert.assertNotNull("riskFormData1");
    }

    @Test
    public void getMyProcessAssetsTest() throws UedmException {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("assets");
        processQuery.setSource(source);

        List<AssetActivitiApprovedResultVo> tobeList = new ArrayList<>();
        List<AssetActivitiApprovedResultVo> doneList = new ArrayList<>();
        AssetActivitiApprovedResultVo item = new AssetActivitiApprovedResultVo();
        AssetActivitiApprovedResultVo item1 = new AssetActivitiApprovedResultVo();
        AssetActivitiApprovedResultVo item2 = new AssetActivitiApprovedResultVo();
        AssetActivitiApprovedResultVo item3 = new AssetActivitiApprovedResultVo();
        AssetActivitiApprovedResultVo item4 = new AssetActivitiApprovedResultVo();
        AssetActivitiApprovedResultVo item5 = new AssetActivitiApprovedResultVo();

        item.setApplicationName("test");
        item.setApplicationTime("2022-10-12 10:10:10");
        item.setFinishTime("2022-10-12 10:10:10");
        item.setId("id");
        item.setStatus("1");
        item.setType(new HashMap<>());
        item.setCurrentProcessor("admin");

        tobeList.add(item);

        doneList.add(item);



        Map<String, List<AssetActivitiApprovedResultVo>> assetsResult = new HashMap<>();
        assetsResult.put("pending",tobeList);
        assetsResult.put("solved",doneList);
        when(assetsRpc.getAssetsOfMine(Mockito.any(),Mockito.any())).thenReturn(assetsResult);

        List<MyProcess> processList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(5);
        when(i18nUtils.getMapFieldByLanguageOption(Mockito.any(),Mockito.any())).thenReturn("assets");
        workBenchServiceImpl.queryAssets(processList,processQuery,countDownLatch,"admin","en-US");
        processQuery.setProcessTimeTo("2099-1-1 10:10:10");
        processQuery.setProcessTimeFrom("1990-1-1 10:10:10");
        processQuery.setAcceptanceTimeTo("2099-1-1 10:10:10");
        processQuery.setAcceptanceTimeFrom("1990-1-1 10:10:10");
        processQuery.setTitle("e");
        workBenchServiceImpl.queryAssets(processList,processQuery,countDownLatch,"admin","en-US");
        assetsResult.remove("solved");
        when(assetsRpc.getAssetsOfMine(Mockito.any(),Mockito.any())).thenReturn(assetsResult);
        workBenchServiceImpl.queryAssets(processList,processQuery,countDownLatch,"admin","en-US");
        assetsResult.remove("pending");
        when(assetsRpc.getAssetsOfMine(Mockito.any(),Mockito.any())).thenReturn(assetsResult);
        workBenchServiceImpl.queryAssets(processList,processQuery,countDownLatch,"admin","en-US");


        List<String> ls= new ArrayList<>();
        ls.add("assets");
        processQuery.setSource(ls);
        workBenchServiceImpl.queryAssets(processList,processQuery,countDownLatch,"admin","en-US");

        Assert.assertNotNull("riskFormData1");
    }
    @Test
    public void getMyProcessInspectTest() throws UedmException {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("inspect");
        processQuery.setSource(source);

        InspectTaskVO item = new InspectTaskVO();
        InspectTaskVO item1 = new InspectTaskVO();
        InspectTaskVO item2 = new InspectTaskVO();
        InspectTaskVO item3 = new InspectTaskVO();
        InspectTaskVO item4 = new InspectTaskVO();
        InspectTaskVO item5 = new InspectTaskVO();
        item.setName("test");
        item.setGmtCreate("2022-10-12 10:10:10");
        item.setGmtModified("2022-10-12 10:10:10");
        item.setId("id");
        item.setStatus(1);
        item.setInspectTaskType(1);
        item1.setName("test");
        item1.setGmtCreate("2022-10-12 10:10:10");
        item1.setGmtModified("2022-10-12 10:10:10");
        item1.setId("id");
        item1.setStatus(2);
        item1.setInspectTaskType(2);
        item2.setName("test");
        item2.setGmtCreate("2022-10-12 10:10:10");
        item2.setGmtModified("2022-10-12 10:10:10");
        item2.setId("id");
        item2.setStatus(3);
        item2.setInspectTaskType(3);
        item3.setName("test");
        item3.setGmtCreate("2022-10-12 10:10:10");
        item3.setGmtModified("2022-10-12 10:10:10");
        item3.setId("id");
        item3.setStatus(4);
        item3.setInspectTaskType(1);
        item4.setName("test");
        item4.setGmtCreate("2022-10-12 10:10:10");
        item4.setGmtModified("2022-10-12 10:10:10");
        item4.setId("id");
        item4.setStatus(5);
        item4.setInspectTaskType(1);

        List<InspectTaskVO> inspectList = new ArrayList<>();
        inspectList.add(item);
        inspectList.add(item1);
        inspectList.add(item2);
        inspectList.add(item3);
        inspectList.add(item4);
        when(inspectRpc.getInspectOfMine(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(inspectList);

        List<MyProcess> processList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(5);
        when(i18nUtils.getMapFieldByLanguageOption(Mockito.any(),Mockito.any())).thenReturn("assets");
        workBenchServiceImpl.queryInspect(processList,processQuery,countDownLatch,"name","123","en-US");
        processQuery.setProcessTimeTo("2099-1-1 10:10:10");
        processQuery.setProcessTimeFrom("1990-1-1 10:10:10");
        processQuery.setAcceptanceTimeTo("2099-1-1 10:10:10");
        processQuery.setAcceptanceTimeFrom("1990-1-1 10:10:10");
        processQuery.setTitle("e");
        workBenchServiceImpl.queryInspect(processList,processQuery,countDownLatch,"name","123","en-US");

        Assert.assertNotNull("riskFormData1");
    }

    @Test
    public void getMyProcessMaintanceTest() throws UedmException {
        ProcessQueryVO processQuery = new ProcessQueryVO();
        processQuery.setPageNum(1);
        processQuery.setPageSize(10);
        List<String> source = new ArrayList<>();
        source.add("maintenance");
        processQuery.setSource(source);
        ProcessQueryVO processQuery22 = new ProcessQueryVO();
        processQuery22.setPageNum(1);
        processQuery22.setPageSize(10);
        List<String> source22 = new ArrayList<>();
        source22.add("11");
        processQuery22.setSource(source22);
        MaintenanceTaskListOfMineDTO result = new MaintenanceTaskListOfMineDTO();
        Map<String, List<MaintenanceTaskListDTO>> maintenanceTaskOfMine = new HashMap<>();
        MaintenanceTaskListOfMineDTO result2 = new MaintenanceTaskListOfMineDTO();
        Map<String, List<MaintenanceTaskListDTO>> maintenanceTaskOfMine2 = new HashMap<>();
        List<MaintenanceTaskListDTO> list1= new ArrayList<>();
        List<MaintenanceTaskListDTO> list2= new ArrayList<>();
        List<MaintenanceTaskListDTO> list3= new ArrayList<>();

        MaintenanceTaskListDTO item = new MaintenanceTaskListDTO();
        MaintenanceTaskListDTO item1 = new MaintenanceTaskListDTO();
        MaintenanceTaskListDTO item2 = new MaintenanceTaskListDTO();
        MaintenanceTaskListDTO item3 = new MaintenanceTaskListDTO();
        MaintenanceTaskListDTO item4 = new MaintenanceTaskListDTO();
        MaintenanceTaskListDTO item5 = new MaintenanceTaskListDTO();
        item.setName("test");
        item.setTaskCreateTime("2022-10-12 10:10:10");
        item.setExecuteTime("2022-10-12 10:10:10");
        item.setId("id");
        item.setStatus(1);

        item1.setName("test");
        item1.setTaskCreateTime("2022-10-12 10:10:10");
        item1.setExecuteTime("2022-10-12 10:10:10");
        item1.setId("id");
        item1.setStatus(2);

        item2.setName("test");
        item2.setTaskCreateTime("2022-10-12 10:10:10");
        item2.setExecuteTime("2022-10-12 10:10:10");
        item2.setId("id");
        item2.setStatus(3);

        item3.setName("test");
        item3.setTaskCreateTime("2022-10-12 10:10:10");
        item3.setExecuteTime("2022-10-12 10:10:10");
        item3.setId("id");
        item3.setStatus(4);

        item4.setName("test");
        item4.setTaskCreateTime("2022-10-12 10:10:10");
        item4.setExecuteTime("2022-10-12 10:10:10");
        item4.setId("id");
        item4.setStatus(5);

        item5.setName("test");
        item5.setTaskCreateTime("2022-10-12 10:10:10");
        item5.setExecuteTime("2022-10-12 10:10:10");
        item5.setId("id");
        item5.setStatus(6);


        list1.add(item);
        list1.add(item1);
        list1.add(item2);
        list1.add(item3);
        list1.add(item4);
        list1.add(item5);

        list2.add(item);
        list2.add(item1);
        list2.add(item2);
        list2.add(item3);
        list2.add(item4);
        list2.add(item5);

        list3.add(item);
        list3.add(item1);
        list3.add(item2);
        list3.add(item3);
        list3.add(item4);
        list3.add(item5);

        maintenanceTaskOfMine.put(TASK_TO_BE_PROCESSED_EN,list1);
        maintenanceTaskOfMine.put(TASK_PROCESSED_EN,list2);
        maintenanceTaskOfMine.put(TASK_MISSING_EN,list3);
        result.setMaintenanceTaskOfMine(maintenanceTaskOfMine);
        result2.setMaintenanceTaskOfMine(maintenanceTaskOfMine2);

        when(maintenanceTaskQueryService.getMaintenanceTaskOfMine(Mockito.any())).thenReturn(result);

        List<MyProcess> processList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(5);
        when(i18nUtils.getMapFieldByLanguageOption(Mockito.any(),Mockito.any())).thenReturn("assets");
        workBenchServiceImpl.queryMaintenance(processList,processQuery,countDownLatch,"name","en-US");
        when(maintenanceTaskQueryService.getMaintenanceTaskOfMine(Mockito.any())).thenReturn(result2);
        workBenchServiceImpl.queryMaintenance(processList,processQuery,countDownLatch,"name","en-US");
        when(maintenanceTaskQueryService.getMaintenanceTaskOfMine(Mockito.any())).thenReturn(result);
        processQuery.setProcessTimeTo("2099-1-1 10:10:10");
        processQuery.setProcessTimeFrom("1990-1-1 10:10:10");
        processQuery.setAcceptanceTimeTo("2099-1-1 10:10:10");
        processQuery.setAcceptanceTimeFrom("1990-1-1 10:10:10");
        processQuery.setTitle("e");
        workBenchServiceImpl.queryMaintenance(processList,processQuery,countDownLatch,"name","en-US");
        workBenchServiceImpl.queryMaintenance(processList,processQuery22,countDownLatch,"name","en-US");
        processQuery.setTitle("9iwu");
        workBenchServiceImpl.queryMaintenance(processList,processQuery,countDownLatch,"name","en-US");

        Assert.assertNotNull("riskFormData1");
    }

    @Test
    public void exportExcelTest() throws UedmException {
        MyProcessListVO myProcessListVO = new MyProcessListVO();
        List<MyProcess> myProcessList = new ArrayList<>();
        MyProcess myProcess = new MyProcess();
        myProcess.setName("name");
        myProcess.setAcceptanceTime("abc");
        myProcess.setProcessTime("abc");
        myProcess.setStatusName("abc");
        myProcess.setSourceName("abc");
        myProcessList.add(myProcess);
        myProcessListVO.setProcessList(myProcessList);
        ProcessQueryVO processQueryVO = new ProcessQueryVO();
        processQueryVO.setSource(new ArrayList<>());
        //Mockito.doReturn(myProcessListVO).when(workBenchServiceImpl).getMyProcessList(processQueryVO,"admin","123",5,"en-us");

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        ThreadPoolExecutor mock = PowerMockito.mock(ThreadPoolExecutor.class);
        Whitebox.setInternalState(workBenchServiceImpl, "workbenchPool", mock);
        workBenchServiceImpl.exportMyProcess("en-us","admin","123",response,processQueryVO);
        workBenchServiceImpl.getExcelData(myProcessListVO,"en-US");
        Assert.assertNotNull("riskFormData1");
    }

}