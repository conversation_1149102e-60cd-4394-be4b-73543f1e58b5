package com.zte.uedm.maintenance.manager.application.executor.impl;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.maintenance.common.I18nService;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository.MaintenancePlanRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceresult.repository.MaintenanceResultRepository;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetask.repository.MaintenanceTaskRepository;
import com.zte.uedm.maintenance.manager.domain.shared.enums.MaintenanceTaskStatusEnums;
import com.zte.uedm.maintenance.manager.infrastructure.client.user.impl.UserServiceRpcImpl;
import com.zte.uedm.maintenance.manager.infrastructure.common.config.EmailAndSMSConfig;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.*;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.po.MaintenancePlanPO;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenancetask.po.MaintenanceTaskPO;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.TaskRuleBean;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.TriggerBaseBean;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.UserBean;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceResultVO;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceTaskExecuteVO;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceUploadResultVO;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceUploadVO;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,LoginHelper.class,Calendar.class})
public class MaintenanceTaskCommandServiceImplTest {
    @InjectMocks
    private MaintenanceTaskCommandServiceImpl maintenanceTaskCommandService;
    @Mock
    private DateTimeService dateTimeService;
    @Mock
    private MaintenancePlanRepository maintenancePlanRepository;
    @Mock
    private MaintenanceTaskRepository maintenanceTaskRepository;
    @Mock
    private JsonService jsonService;
    @Mock
    private OperationLogUtils operationLogUtils;
    @Mock
    private UserServiceRpcImpl userServiceRpc;
    @Mock
    private SendSmsUtil sendSmsUtil;
    @Mock
    private SendEmailUtil sendEmailUtil;
    @Mock
    private EmailAndSMSConfig emailAndSMSConfig;
    @Mock
    private MaintenanceResultRepository maintenanceResultRepository;
    @Mock
    private I18nUtils i18nUtils;
    @Mock
    private I18nService i18nService;

    @Before
    public void setUp() throws ParseException {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn(9999999999990L).when(dateTimeService).getLongTime(Mockito.any());
        Mockito.doReturn("2022-07-08 00:00:00").when(dateTimeService).getCurrentTime();
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(LoginHelper.class);
        Mockito.doNothing().when(operationLogUtils).sendMaintenanceTaskNotExecuteLog(Mockito.any(),Mockito.any());
        Mockito.doReturn(true).when(sendSmsUtil).send(Mockito.anyList(),Mockito.anyString());
        Mockito.doNothing().when(sendEmailUtil).sendEmail(Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.doReturn(new ArrayList<>()).when(emailAndSMSConfig).getAllIp();
        Mockito.doReturn(99999999999990L).when(dateTimeService).getLongTime(Mockito.any());

        when(CommonUtils.getTomorrowZeroTime()).thenReturn("2022-07-08 00:00:00");
        when(CommonUtils.getNextWeekZeroTime()).thenReturn("2022-07-08 00:00:00");
        when(CommonUtils.getNextMonthZeroTime()).thenReturn("2022-07-08 00:00:00");
    }

    @Test
    public void generateTaskTestDay() throws UedmException, ParseException {
        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("day");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertEquals(1,taskPOList.size());
    }

    @Test
    public void generateTaskTestWeekError()
    {
        try
        {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
            when(CommonUtils.getNextWeekZeroTime(Mockito.any())).thenReturn(time);
            Calendar instance = Calendar.getInstance();
            instance.setTime(date);
            int weekDayIndex = instance.get(Calendar.DAY_OF_WEEK) - 1;

            List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
            MaintenancePlanPO plan = new MaintenancePlanPO();
            plan.setExecuteRule("a");
            plan.setId("aa");
            needGenerateTaskPlan.add(plan);

            TaskRuleBean role = new TaskRuleBean();
            role.setGrain("week");
            role.setConsumeTime(2);
            role.setFrequency(1);
            TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
            triggerBaseBean.setIndex(0);
            triggerBaseBean.setWeekDayIndex(weekDayIndex + 1);
            triggerBaseBean.setBeginTime("15:00");
            role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
            Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
            Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
            List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
            Assert.assertNotEquals(10,taskPOList.size());
        }
        catch (Exception e)
        {
            Assert.assertNotEquals("", e.getMessage());
        }
    }

    @Test
    public void generateTaskTestWeek() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextWeekZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int weekDayIndex = instance.get(Calendar.DAY_OF_WEEK) - 1;

        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("week");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setWeekDayIndex(weekDayIndex);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertNotNull(taskPOList);
    }

    @Test
    public void generateTaskTestMonthError() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);

        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("month");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthDayIndex(monthDayIndex + 1);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertNotEquals(9850,taskPOList.size());
    }

    @Test
    public void generateTaskTestMonth() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);

        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("month");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthDayIndex(monthDayIndex);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertNotNull(taskPOList);
    }

    @Test
    public void generateTaskTestQuarterError() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);
        int monthIndex = getMonthIndexOfQuarter(instance);
        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("quarter");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthIndex(monthIndex + 1);
        triggerBaseBean.setMonthDayIndex(monthDayIndex);
        triggerBaseBean.setBeginTime("15:00");

        TriggerBaseBean triggerBaseBean1 = new TriggerBaseBean();
        triggerBaseBean1.setIndex(0);
        triggerBaseBean1.setMonthIndex(monthIndex);
        triggerBaseBean1.setMonthDayIndex(monthDayIndex + 1);
        triggerBaseBean1.setBeginTime("15:00");

        role.setTaskDatetime(Arrays.asList(triggerBaseBean,triggerBaseBean1));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertEquals(0,taskPOList.size());
    }

    @Test
    public void generateTaskTestQuarter() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);
        int monthIndex = getMonthIndexOfQuarter(instance);
        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("quarter");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthIndex(monthIndex);
        triggerBaseBean.setMonthDayIndex(monthDayIndex);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertNotNull(taskPOList);
    }

    @Test
    public void generateTaskTestHalfYear() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);
        int monthIndex = getMonthIndexOfHalfYear(instance);
        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("halfYear");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthIndex(monthIndex);
        triggerBaseBean.setMonthDayIndex(monthDayIndex);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Mockito.doThrow(new UedmException(-1,"aaa")).when(jsonService).jsonToObject(Mockito.any(),Mockito.any());
        maintenanceTaskCommandService.generateTask();
        Assert.assertNotNull(taskPOList);
    }

    @Test
    public void generateTaskTestYear() throws UedmException, ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time = simpleDateFormat.format(date).substring(0, 10) + " 00:00:00";
        when(CommonUtils.getNextMonthZeroTime(Mockito.any())).thenReturn(time);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        int monthDayIndex = instance.get(Calendar.DAY_OF_MONTH);
        int monthIndex = instance.get(Calendar.MONTH) + 1;
        List<MaintenancePlanPO> needGenerateTaskPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setExecuteRule("a");
        plan.setId("aa");
        needGenerateTaskPlan.add(plan);

        TaskRuleBean role = new TaskRuleBean();
        role.setGrain("year");
        role.setConsumeTime(2);
        role.setFrequency(1);
        TriggerBaseBean triggerBaseBean = new TriggerBaseBean();
        triggerBaseBean.setIndex(0);
        triggerBaseBean.setMonthIndex(monthIndex);
        triggerBaseBean.setMonthDayIndex(monthDayIndex);
        triggerBaseBean.setBeginTime("15:00");
        role.setTaskDatetime(Collections.singletonList(triggerBaseBean));
        Mockito.doReturn(role).when(jsonService).jsonToObject("a", TaskRuleBean.class);
        Mockito.doReturn(needGenerateTaskPlan).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertNotNull(taskPOList);
    }

    public int getMonthIndexOfQuarter(Calendar instance){
        int monthIndexOfYear = instance.get(Calendar.MONTH) + 1;
        return monthIndexOfYear % 3 == 0 ? 3 : monthIndexOfYear % 3;
    }

    public int getMonthIndexOfHalfYear(Calendar instance){
        int monthIndexOfYear = instance.get(Calendar.MONTH) + 1;
        return monthIndexOfYear <= 6 ? monthIndexOfYear : monthIndexOfYear - 6;
    }

    @Test
    public void checkExpiredTaskTestZeroTime(){
        Mockito.doReturn("2022-07-11 00:00:00").when(dateTimeService).getCurrentTime();
        List<MaintenanceTaskPO> timeOutTask = new ArrayList<>();
        Mockito.doReturn(new ArrayList<>()).when(maintenanceTaskRepository).selectNotExecuteTimeOutTask(Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.checkExpiredTask();
        Assert.assertEquals(0,taskPOList.size());

        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setName("aaa");
        timeOutTask.add(task);
        Mockito.doReturn(timeOutTask).when(maintenanceTaskRepository).selectNotExecuteTimeOutTask(Mockito.any());
        List<MaintenanceTaskPO> taskPOList1 = maintenanceTaskCommandService.checkExpiredTask();
        Assert.assertEquals(1,taskPOList1.size());
    }

    @Test
    public void checkExpiredTaskTest(){
        Mockito.doReturn("2022-07-11 10:00:00").when(dateTimeService).getCurrentTime();
        List<MaintenanceTaskPO> timeOutTask = new ArrayList<>();
        Mockito.doReturn(new ArrayList<>()).when(maintenanceTaskRepository).selectWaitExecuteTimeOutTask(Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.checkExpiredTask();
        Assert.assertEquals(0,taskPOList.size());

        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setName("aaa");
        timeOutTask.add(task);
        Mockito.doReturn(timeOutTask).when(maintenanceTaskRepository).selectWaitExecuteTimeOutTask(Mockito.any());
        List<MaintenanceTaskPO> taskPOList1 = maintenanceTaskCommandService.checkExpiredTask();
        Assert.assertEquals(0,taskPOList1.size());
    }

    @Test
    public void sendMsgPrepareSecondTestPhone() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method sendMsgPrepareSecond = maintenanceTaskCommandService.getClass()
                .getDeclaredMethod("sendMsgPrepareSecond", Integer.class, String.class, List.class, MaintenanceTaskPO.class,MaintenanceTaskStatusEnums.class,Map.class);
        sendMsgPrepareSecond.setAccessible(true);
        UserBean userBean = new UserBean();
        UserBean userBean1 = new UserBean();
        UserBean userBean2 = new UserBean();
        userBean.setUserName("aaa");
        userBean.setFullName("aaa");
        userBean.setPhoneNumberTwo("aaa");
        userBean.setEmail("aaa");
        userBean1.setUserName("bbb");
        userBean1.setFullName("bbb");
        userBean1.setPhoneNumberTwo("aaa");
        userBean1.setEmail("aaa");
        userBean2.setUserName("ccc");
        userBean2.setFullName("ccc");
        userBean2.setPhoneNumberTwo("aaa");
        userBean2.setEmail("aaa");
        Mockito.doReturn(userBean).when(userServiceRpc).getUsersInfoByName("aaa","zh_CN");
        Mockito.doReturn(userBean1).when(userServiceRpc).getUsersInfoByName("bbb","zh_CN");
        Mockito.doReturn(userBean2).when(userServiceRpc).getUsersInfoByName("ccc","zh_CN");
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setName("aaa");
        task.setTaskBeginTime("aaa");
        task.setTaskEndTime("aaa");
        sendMsgPrepareSecond.invoke(maintenanceTaskCommandService,2,"aaa",new ArrayList<>(Arrays.asList("aaa","bbb","ccc")),task,MaintenanceTaskStatusEnums.WAIT_EXECUTE,new HashedMap<>());
        Assert.assertEquals("aaa",task.getName());
    }

    @Test
    public void sendMsgPrepareSecondTestMail() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method sendMsgPrepareSecond = maintenanceTaskCommandService.getClass()
                .getDeclaredMethod("sendMsgPrepareSecond", Integer.class, String.class, List.class, MaintenanceTaskPO.class,MaintenanceTaskStatusEnums.class,Map.class);
        sendMsgPrepareSecond.setAccessible(true);
        UserBean userBean = new UserBean();
        UserBean userBean1 = new UserBean();
        UserBean userBean2 = new UserBean();
        userBean.setUserName("aaa");
        userBean.setFullName("aaa");
        userBean.setPhonenumber("aaa");
        userBean.setEmail("aaa");
        userBean1.setUserName("bbb");
        userBean1.setFullName("bbb");
        userBean1.setPhonenumber("aaa");
        userBean1.setEmail("aaa");
        userBean2.setUserName("ccc");
        userBean2.setPhonenumber("aaa");
        userBean2.setEmail("aaa");

        Mockito.doReturn(userBean).when(userServiceRpc).getUsersInfoByName("aaa","zh_CN");
        Mockito.doReturn(userBean1).when(userServiceRpc).getUsersInfoByName("bbb","zh_CN");
        Mockito.doReturn(userBean2).when(userServiceRpc).getUsersInfoByName("ccc","zh_CN");
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setName("aaa");
        task.setTaskBeginTime("aaa");
        task.setTaskEndTime("aaa");
        sendMsgPrepareSecond.invoke(maintenanceTaskCommandService,3,"aaa",new ArrayList<>(Arrays.asList("aaa","bbb","ccc")),task,MaintenanceTaskStatusEnums.WAIT_EXECUTE,new HashedMap<>());
        Assert.assertEquals("aaa",task.getName());
    }

    @Test
    public void getExecutorTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getExecutor = maintenanceTaskCommandService.getClass().getDeclaredMethod("getExecutor",List.class, List.class);
        getExecutor.setAccessible(true);

        Object invoke = getExecutor.invoke(maintenanceTaskCommandService, new ArrayList<>(), new ArrayList<>());
        Assert.assertEquals(new ArrayList<>().toString(),invoke.toString());
    }

    @Test
    public void getResponsiblePersonTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getResponsiblePerson = maintenanceTaskCommandService.getClass().getDeclaredMethod("getResponsiblePerson",List.class, String.class);
        getResponsiblePerson.setAccessible(true);

        Object invoke = getResponsiblePerson.invoke(maintenanceTaskCommandService, new ArrayList<>(), "");
        Assert.assertEquals("",invoke.toString());

        Object invoke1 = getResponsiblePerson.invoke(maintenanceTaskCommandService, new ArrayList<>(), "aaa");
        Assert.assertEquals("aaa",invoke1.toString());
    }

    @Test
    public void sendMsgPrepareFirstTest() throws NoSuchMethodException, UedmException, InvocationTargetException, IllegalAccessException {
        Method sendMsgPrepareFirst = maintenanceTaskCommandService.getClass().getDeclaredMethod("sendMsgPrepareFirst",List.class, List.class, MaintenanceTaskStatusEnums.class);
        sendMsgPrepareFirst.setAccessible(true);

        List<MaintenanceTaskPO> taskNew = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setMaintenancePlanId("aaa");
        taskNew.add(task);

        List<MaintenancePlanPO> planList = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setId("aaa");
        plan.setResponsiblePerson("aaa");
        plan.setReminderMode(2);
        planList.add(plan);

        Mockito.doReturn(new ArrayList<>(Collections.singletonList("aaa"))).when(jsonService).jsonToObject(Mockito.any(),Mockito.any(),Mockito.any());
        sendMsgPrepareSecondTestPhone();

        Object invoke = sendMsgPrepareFirst.invoke(maintenanceTaskCommandService, taskNew, planList,MaintenanceTaskStatusEnums.COMPLETE);
        Assert.assertNull(invoke);
    }

    @Test
    public void generateTaskTestOne() throws UedmException, ParseException {
        Mockito.doReturn(new ArrayList<>()).when(maintenancePlanRepository).selectNeedGenerateTaskPlan(Mockito.any(),Mockito.any(),Mockito.any());
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.generateTask();
        Assert.assertEquals(0,taskPOList.size());
    }

    @Test
    public void taskCompensationTest() throws ParseException, UedmException {
        Mockito.doReturn(new ArrayList<>()).when(maintenancePlanRepository).selectAllEnabledPlan();
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.taskCompensation(new ArrayList<>());
        Assert.assertEquals(0,taskPOList.size());

        List<MaintenanceTaskPO> allTask = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        allTask.add(task);
        task.setMaintenancePlanId("aaa");
        task.setTaskBeginTime("aaa");
        task.setTaskEndTime("aaa");
        Mockito.doReturn(allTask).when(maintenanceTaskRepository).getAllTaskByPlanIdList(Mockito.any());


        List<MaintenancePlanPO> allPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setId("aaa");
        plan.setLastTaskGenerateTime("2022-07-13 15:38:30");
        MaintenancePlanPO plan1 = new MaintenancePlanPO();
        plan1.setId("aaa");
        plan1.setLastTaskGenerateTime("2022-07-11 15:38:30");
        plan1.setPlanEndTime("2022-07-11 11:38:30");
        allPlan.add(plan);
        allPlan.add(plan1);
        Mockito.doReturn(allPlan).when(maintenancePlanRepository).selectAllEnabledPlan();
        when(CommonUtils.getTodayZeroTime()).thenReturn("2022-07-13 15:38:30");
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,-1);
        when(CommonUtils.getCalendarByStr(Mockito.any())).thenReturn(instance);
        when(CommonUtils.getCalendarByStr("2022-07-13 15:38:30")).thenReturn(Calendar.getInstance());
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,3);
        when(CommonUtils.getStrZeroTime("2022-07-11 11:38:30")).thenReturn(instance1);

        TaskRuleBean taskRuleBean = new TaskRuleBean();
        taskRuleBean.setGrain("day");
        taskRuleBean.setConsumeTime(2);
        List<TriggerBaseBean> taskDatetime = new ArrayList<>();
        TriggerBaseBean triggerBaseBean1 = new TriggerBaseBean();
        TriggerBaseBean triggerBaseBean2 = new TriggerBaseBean();
        taskDatetime.add(triggerBaseBean1);
        taskDatetime.add(triggerBaseBean2);
        taskRuleBean.setTaskDatetime(taskDatetime);
        Mockito.doReturn(taskRuleBean).when(jsonService).jsonToObject(Mockito.any(),Mockito.any());

        List<MaintenanceTaskPO> taskPOList1 = maintenanceTaskCommandService.taskCompensation(new ArrayList<>());
        Assert.assertNotNull(taskPOList1);

        when(CommonUtils.getTodayZeroTime()).thenThrow(new RuntimeException("aaa"));
        List<MaintenanceTaskPO> taskPOList2 = maintenanceTaskCommandService.taskCompensation(new ArrayList<>());
        Assert.assertNotNull(taskPOList2);

    }

    @Test
    public void testError() throws ParseException, UedmException {
        Mockito.doReturn(new ArrayList<>()).when(maintenancePlanRepository).selectAllEnabledPlan();
        List<MaintenanceTaskPO> taskPOList = maintenanceTaskCommandService.taskCompensation(new ArrayList<>());
        Assert.assertEquals(0,taskPOList.size());

        List<MaintenanceTaskPO> allTask = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        allTask.add(task);
        task.setMaintenancePlanId("aaa");
        task.setTaskBeginTime("aaa");
        task.setTaskEndTime("aaa");
        Mockito.doReturn(allTask).when(maintenanceTaskRepository).getAllTaskByPlanIdList(Mockito.any());


        List<MaintenancePlanPO> allPlan = new ArrayList<>();
        MaintenancePlanPO plan = new MaintenancePlanPO();
        plan.setLastTaskGenerateTime("2022-07-13 15:38:30");
        MaintenancePlanPO plan1 = new MaintenancePlanPO();
        plan1.setLastTaskGenerateTime("2022-07-11 15:38:30");
        plan1.setPlanEndTime("2022-07-11 11:38:30");
        allPlan.add(plan);
        allPlan.add(plan1);
        Mockito.doReturn(allPlan).when(maintenancePlanRepository).selectAllEnabledPlan();
        when(CommonUtils.getTodayZeroTime()).thenReturn("2022-07-13 15:38:30");
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,-1);
        when(CommonUtils.getCalendarByStr(Mockito.any())).thenReturn(instance);
        when(CommonUtils.getCalendarByStr("2022-07-13 15:38:30")).thenReturn(Calendar.getInstance());
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,3);
        when(CommonUtils.getStrZeroTime("2022-07-11 11:38:30")).thenReturn(instance1);

        TaskRuleBean taskRuleBean = new TaskRuleBean();
        taskRuleBean.setGrain("day");

        List<TriggerBaseBean> taskDatetime = new ArrayList<>();
        TriggerBaseBean triggerBaseBean1 = new TriggerBaseBean();
        TriggerBaseBean triggerBaseBean2 = new TriggerBaseBean();
        taskDatetime.add(triggerBaseBean1);
        taskDatetime.add(triggerBaseBean2);
        taskRuleBean.setTaskDatetime(taskDatetime);
        Mockito.doReturn(taskRuleBean).when(jsonService).jsonToObject(Mockito.any(),Mockito.any());

        when(CommonUtils.getTomorrowZeroTime(null)).thenThrow(new RuntimeException("aaa"));
        List<MaintenanceTaskPO> taskPOList3 = maintenanceTaskCommandService.taskCompensation(new ArrayList<>());
        Assert.assertEquals(0,taskPOList3.size());
    }

    @Test
    public void temporaryOrCompleteTest(){
        MaintenanceTaskPO taskPO=new MaintenanceTaskPO();
        taskPO.setMaintenancePlanId("111");
        taskPO.setComplete(true);
        taskPO.setTaskEndTime("2022-06-06 14:00:00");
        taskPO.setStatus(1);
        taskPO.setTaskBeginTime("2022-05-05 00:00:00");
        MaintenancePlanPO planPO=new MaintenancePlanPO();
        planPO.setId("bbb");
        Mockito.doReturn(taskPO).when(maintenanceTaskRepository).findOneById(Mockito.any());
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        when(LoginHelper.getLoginUserName()).thenReturn("aaa");
        MaintenanceTaskExecuteVO maintenanceTaskExecuteVO=new MaintenanceTaskExecuteVO();
        maintenanceTaskExecuteVO.setType(1);
        ResponseBean responseBean=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(-100),responseBean.getCode());
        MaintenanceResultVO resultVO=new MaintenanceResultVO();
        resultVO.setMaintenanceTaskId("aaa");
        resultVO.setConclusion(null);
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        ResponseBean responseBean2=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(0),responseBean2.getCode());
        resultVO.setConclusion(1);
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        ResponseBean responseBean6=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(0),responseBean6.getCode());
        resultVO.setConclusion(1);
        taskPO.setExecutor("bbb");
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        ResponseBean responseBean7=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(-202),responseBean7.getCode());
        taskPO.setExecutor(null);
        Mockito.doReturn(planPO).when(maintenancePlanRepository).getById(Mockito.any(String.class));
        maintenanceTaskExecuteVO.setType(2);
        ResponseBean responseBean3=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(0),responseBean3.getCode());
        taskPO.setPreStatus(false);
        taskPO.setTaskEndTime("2022-06-05 00:00:00");
        taskPO.setStatus(2);
        ResponseBean responseBean4=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(0),responseBean4.getCode());
        taskPO.setPreStatus(false);
        taskPO.setTaskEndTime("2022-06-06 12:00:00");
        taskPO.setStatus(2);
        resultVO.setConclusion(2);
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        ResponseBean responseBean5=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(0),responseBean5.getCode());
        taskPO.setTaskBeginTime("2022-07-07 00:00:00");
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        ResponseBean responseBean8=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(Integer.valueOf(-203),responseBean8.getCode());
    }

    @Test
    public void temporaryOrCompleteTest2(){
        MaintenanceTaskExecuteVO maintenanceTaskExecuteVO=new MaintenanceTaskExecuteVO();
        maintenanceTaskExecuteVO.setType(1);
        MaintenanceResultVO resultVO=new MaintenanceResultVO();
        resultVO.setMaintenanceTaskId("aaa");
        maintenanceTaskExecuteVO.setResult(Collections.singletonList(resultVO));
        Mockito.doThrow(new RuntimeException("eee")).when(maintenanceTaskRepository).findOneById(Mockito.any());
        ResponseBean responseBean3=maintenanceTaskCommandService.temporaryOrComplete(maintenanceTaskExecuteVO);
        Assert.assertEquals(responseBean3.getMessage(),"eee");
    }

    @Test
    public void checkTaskIsExistsTest() throws NoSuchMethodException{
        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method checkTaskIsExists = aClass.getDeclaredMethod("checkTaskIsExists", Set.class, MaintenanceTaskPO.class, MaintenancePlanPO.class);
        checkTaskIsExists.setAccessible(true);
        Set<String> allTask = new HashSet<>();
        allTask.add("aaa-aaa-aaa");

        MaintenanceTaskPO task1 = new MaintenanceTaskPO();
        task1.setMaintenancePlanId("aaa");
        task1.setTaskBeginTime("aaa");
        task1.setTaskEndTime("aaa");

        MaintenancePlanPO planPO = new MaintenancePlanPO();
        planPO.setId("aaa");

        boolean success = true;
        try{
            checkTaskIsExists.invoke(maintenanceTaskCommandService,allTask,task1,planPO);
        }catch (Exception e){
            success = false;
        }
        Assert.assertFalse(success);
    }


    @Test
    public void getEndCalenderTest() throws NoSuchMethodException, ParseException, InvocationTargetException, IllegalAccessException {
        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method checkTaskIsExists = aClass.getDeclaredMethod("getEndCalender", String.class, String.class);
        checkTaskIsExists.setAccessible(true);
        when(CommonUtils.getCalendarByStr(Mockito.any())).thenReturn(Calendar.getInstance());
        when(CommonUtils.getStrZeroTime(Mockito.any())).thenReturn(Calendar.getInstance());

        Object invoke = checkTaskIsExists.invoke(maintenanceTaskCommandService, "aaa", "week");
        Assert.assertTrue(invoke instanceof Calendar);

        Object invoke1 = checkTaskIsExists.invoke(maintenanceTaskCommandService, "aaa", "month");
        Assert.assertTrue(invoke1 instanceof Calendar);
    }

    @Test
    public void getRealGenerateTimeByGrainTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method aaa = aClass.getDeclaredMethod("getRealGenerateTimeByGrain", String.class, String.class);
        aaa.setAccessible(true);

        Object aaa1 = aaa.invoke(maintenanceTaskCommandService, "aaa", "");
        Assert.assertEquals("aaa",aaa1);
    }
    @Test
    public void setTaskTimeInfoByTriggerTest() {
        Exception e=null;
        TriggerBaseBean triggerBaseBean=new TriggerBaseBean();
        triggerBaseBean.setBeginTime("10:00");
        MaintenanceTaskPO taskPO=new MaintenanceTaskPO();
        TaskRuleBean taskRuleBean=new TaskRuleBean();
        taskRuleBean.setGrain("day");
        taskRuleBean.setConsumeTime(1);
        try{
            Mockito.doReturn(null).when(dateTimeService).getLongTime(Mockito.any());
            maintenanceTaskCommandService.setTaskTimeInfoByTrigger(triggerBaseBean,taskRuleBean,null,taskPO);
        }catch (Exception exception){
            e=exception;
        }
        Assert.assertNotNull(e);
    }

    @Test
    public void lockExecutorByIdTest(){
        MaintenanceTaskPO po=new MaintenanceTaskPO();
        when(LoginHelper.getLoginUserName()).thenReturn("aaa");
        Mockito.doReturn(po).when(maintenanceTaskRepository).findOneById(Mockito.any());
        ResponseBean responseBean=maintenanceTaskCommandService.lockExecutorById("aaa","zh_CN");
        Assert.assertEquals(Integer.valueOf(0),responseBean.getCode());
        po.setExecutor("aaa");
        ResponseBean responseBean2=maintenanceTaskCommandService.lockExecutorById("aaa","zh_CN");
        Assert.assertEquals(Integer.valueOf(0),responseBean2.getCode());
        po.setExecutor("bbb");
        ResponseBean responseBean3=maintenanceTaskCommandService.lockExecutorById("aaa","zh_CN");
        Assert.assertEquals(Integer.valueOf(-201),responseBean3.getCode());
    }

    @Test
    public void endTimeCheckTest(){
        try {
            maintenanceTaskCommandService.endTimeCheck(1L);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof UedmException);
        }
    }

    @Test
    public void getTimeOutWaitExecuteTaskTest() throws Exception {
        List<MaintenanceTaskPO> timeOutTaskList = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setTaskEndTime("2022-10-28 17:00:00");
        MaintenanceTaskPO task1 = new MaintenanceTaskPO();
        task1.setTaskEndTime("2022-10-26 17:00:00");
        timeOutTaskList.add(task);
        timeOutTaskList.add(task1);

        Date parse = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-10-26 00:00:00");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);

        PowerMockito.mockStatic(Calendar.class);
        when(Calendar.getInstance()).thenReturn(calendar);

        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method getTimeOutWaitExecuteTask = aClass.getDeclaredMethod("getTimeOutWaitExecuteTask", List.class, String.class);
        getTimeOutWaitExecuteTask.setAccessible(true);

        Object invoke = getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService, timeOutTaskList, "2022-10-28 17:00:00");
        Assert.assertTrue(invoke instanceof List);
    }

    @Test
    public void getTimeOutNotExecuteTaskTest() throws Exception {
        List<MaintenanceTaskPO> timeOutTaskList = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setTaskEndTime("2022-10-28 17:00:00");
        MaintenanceTaskPO task1 = new MaintenanceTaskPO();
        task1.setTaskEndTime("2022-10-26 17:00:00");
        timeOutTaskList.add(task);
        timeOutTaskList.add(task1);

        Date parse = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-10-26 00:00:00");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);

        PowerMockito.mockStatic(Calendar.class);
        when(Calendar.getInstance()).thenReturn(calendar);

        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method getTimeOutWaitExecuteTask = aClass.getDeclaredMethod("getTimeOutNotExecuteTask", List.class, String.class);
        getTimeOutWaitExecuteTask.setAccessible(true);

        Object invoke = getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService, timeOutTaskList, "2022-10-28 17:00:00");
        Assert.assertTrue(invoke instanceof List);
    }

    @Test
    public void getTomorrowZeroTimeTest() throws Exception {

        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method getTimeOutWaitExecuteTask = aClass.getDeclaredMethod("getTomorrowZeroTime",String.class);
        getTimeOutWaitExecuteTask.setAccessible(true);

        Object invoke = getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService,"2022-10-28 00:00:00");
        Assert.assertTrue(invoke instanceof String);
    }

    @Test
    public void updateTaskTest() throws Exception {

        Class<? extends MaintenanceTaskCommandServiceImpl> aClass = maintenanceTaskCommandService.getClass();
        Method getTimeOutWaitExecuteTask = aClass.getDeclaredMethod("updateTask",MaintenanceTaskStatusEnums.class,List.class,String.class);
        getTimeOutWaitExecuteTask.setAccessible(true);

        getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService,MaintenanceTaskStatusEnums.WAIT_EXECUTE_TIME_OUT,new ArrayList<>(),"2022-10-28 00:00:00");

        List<MaintenanceTaskPO> timeOutTaskList = new ArrayList<>();
        MaintenanceTaskPO task = new MaintenanceTaskPO();
        task.setTaskEndTime("2022-10-28 17:00:00");
        task.setName("aaa");
        task.setMaintenancePlanId("aaa");
        MaintenanceTaskPO task1 = new MaintenanceTaskPO();
        task1.setTaskEndTime("2022-10-26 17:00:00");
        task1.setName("aaa");
        task1.setMaintenancePlanId("aaa");

        timeOutTaskList.add(task);
        timeOutTaskList.add(task1);

        getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService,MaintenanceTaskStatusEnums.WAIT_EXECUTE_TIME_OUT,timeOutTaskList,"2022-10-28 00:00:00");
        getTimeOutWaitExecuteTask.invoke(maintenanceTaskCommandService,MaintenanceTaskStatusEnums.NOT_EXECUTE_WITH_TIME_OUT,timeOutTaskList,"2022-10-28 00:00:00");

        Assert.assertEquals(2,timeOutTaskList.size());
    }

    @Test
    public void uploadTaskTest(){
        List<MaintenanceUploadVO> maintenanceUploadVOS=new ArrayList<>();
        MaintenanceUploadVO error1=new MaintenanceUploadVO();
        maintenanceUploadVOS.add(error1);
        MaintenanceUploadVO error2=new MaintenanceUploadVO();
        error2.setTaskId("1");
        maintenanceUploadVOS.add(error2);
        MaintenanceUploadVO error3=new MaintenanceUploadVO();
        error3.setTaskId("1");
        error3.setTaskName("aaa");
        maintenanceUploadVOS.add(error3);
        MaintenanceUploadVO error4=new MaintenanceUploadVO();
        error4.setTaskId("1");
        error4.setTaskName("aaa");
        error4.setRealExecutor("aaa");
        maintenanceUploadVOS.add(error4);
        MaintenanceUploadVO error5=new MaintenanceUploadVO();
        error5.setTaskId("1");
        error5.setTaskName("aaa");
        error5.setRealExecutor("aaa");
        error5.setStatus(100);
        maintenanceUploadVOS.add(error5);
        MaintenanceUploadVO error6=new MaintenanceUploadVO();
        error6.setTaskId("1");
        error6.setTaskName("aaa");
        error6.setRealExecutor("aaa");
        error6.setStatus(2);
        maintenanceUploadVOS.add(error6);
        MaintenanceUploadVO error7=new MaintenanceUploadVO();
        error7.setTaskId("1");
        error7.setTaskName("aaa");
        error7.setRealExecutor("aaa");
        error7.setStatus(2);
        List<MaintenanceUploadResultVO> maintenanceResult=new ArrayList<>();
        MaintenanceUploadResultVO resultVO=new MaintenanceUploadResultVO();
        resultVO.setMaintenanceObjId("aaa");
        maintenanceResult.add(resultVO);
        MaintenanceUploadResultVO resultVO2=new MaintenanceUploadResultVO();
        resultVO2.setMaintenanceObjId("aaa");
        resultVO2.setMaintenanceItemId("bbb");
        resultVO2.setMaintenanceObjId("aaa");
        resultVO2.setMaintenanceTemplateId("aaa");
        resultVO2.setMaintenanceItemName("aaa");
        maintenanceResult.add(resultVO2);
        MaintenanceUploadResultVO resultVO3=new MaintenanceUploadResultVO();
        resultVO3.setMaintenanceObjId("aaa");
        resultVO3.setMaintenanceItemId("bbb");
        resultVO3.setMaintenanceObjName("aaa");
        resultVO3.setMaintenanceTemplateId("aaa");
        resultVO3.setMaintenanceItemName("aaa");
        resultVO3.setConclusion(1);
        maintenanceResult.add(resultVO3);
        error7.setMaintenanceResult(maintenanceResult);
        maintenanceUploadVOS.add(error7);
        MaintenanceUploadVO uploadVO=new MaintenanceUploadVO();
        uploadVO.setTaskId("1");
        uploadVO.setTaskName("aaa");
        uploadVO.setRealExecutor("aaa");
        uploadVO.setStatus(2);
        List<MaintenanceUploadResultVO> uploadVOResults=new ArrayList<>();
        uploadVOResults.add(resultVO3);
        uploadVO.setMaintenanceResult(uploadVOResults);
        maintenanceUploadVOS.add(uploadVO);
        MaintenanceUploadVO uploadVO2=new MaintenanceUploadVO();
        uploadVO2.setTaskId("2");
        uploadVO2.setTaskName("aaa");
        uploadVO2.setRealExecutor("aaa");
        uploadVO2.setStatus(2);
        List<MaintenanceUploadResultVO> uploadVOResult2=new ArrayList<>();
        uploadVOResult2.add(resultVO3);
        uploadVO2.setMaintenanceResult(uploadVOResult2);
        maintenanceUploadVOS.add(uploadVO2);
        MaintenanceUploadVO uploadVO3=new MaintenanceUploadVO();
        uploadVO3.setTaskId("3");
        uploadVO3.setTaskName("aaa");
        uploadVO3.setRealExecutor("aaa");
        uploadVO3.setStatus(2);
        List<MaintenanceUploadResultVO> uploadVOResult3=new ArrayList<>();
        uploadVOResult3.add(resultVO3);
        uploadVO3.setMaintenanceResult(uploadVOResult3);
        maintenanceUploadVOS.add(uploadVO3);
        MaintenanceTaskPO taskPO=new MaintenanceTaskPO();
        taskPO.setMaintenancePlanId("111");
        taskPO.setComplete(true);
        taskPO.setTaskEndTime("2022-06-06 14:00:00");
        taskPO.setStatus(1);
        taskPO.setTaskBeginTime("2022-05-05 00:00:00");
        MaintenancePlanPO planPO=new MaintenancePlanPO();
        planPO.setId("bbb");
        Mockito.doReturn(taskPO).when(maintenanceTaskRepository).findOneById("1");
        Mockito.doReturn(planPO).when(maintenancePlanRepository).getById(Mockito.any(String.class));
        MaintenanceTaskPO taskPO2=new MaintenanceTaskPO();
        taskPO2.setMaintenancePlanId("111");
        taskPO2.setComplete(false);
        taskPO2.setTaskEndTime("2022-06-06 14:00:00");
        taskPO2.setStatus(1);
        taskPO2.setTaskBeginTime("2022-05-05 00:00:00");
        Mockito.doReturn(taskPO2).when(maintenanceTaskRepository).findOneById("2");
        Mockito.doReturn(null).when(maintenanceTaskRepository).findOneById("3");
        Mockito.doReturn("error").when(i18nService).getMessage(Mockito.any(),Mockito.any());
        Mockito.doThrow(new RuntimeException("aaa")).when(operationLogUtils).sendExecuteTaskLog(Mockito.any(),Mockito.anyList());
        ResponseBean responseBean=maintenanceTaskCommandService.uploadTask(maintenanceUploadVOS);
        Assert.assertEquals(responseBean.getTotal(),Integer.valueOf("10"));
    }

    @Test
    public void testGetUserInfo() {
        String responsiblePerson = "Alice";
        List<String> executor = Arrays.asList("Bob", "Charlie", "Alice"); // 含重复项

        // 模拟userServiceRpc返回的用户信息
        UserBean userAlice = new UserBean();
        userAlice.setUserName("Alice");

        UserBean userBob = new UserBean();
        userBob.setUserName("Bob");

        UserBean userCharlie = new UserBean();
        userCharlie.setUserName("Charlie");

        when(userServiceRpc.getUsersInfoByName("Alice", "zh_CN")).thenReturn(userAlice);
        when(userServiceRpc.getUsersInfoByName("Bob", "zh_CN")).thenReturn(userBob);
        when(userServiceRpc.getUsersInfoByName("Charlie", "zh_CN")).thenReturn(userCharlie);

        // 调用方法
        List<UserBean> result = maintenanceTaskCommandService.getUserInfo(responsiblePerson, executor, new HashMap<>());

        // 断言返回值大小正确（去重后：3个）
        assertEquals(3, result.size());

    }
}
