package com.zte.uedm.maintenance.duty.application.scheduler;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.ApplicationArguments;



public class DutyAppInitializerTest {
    @InjectMocks
    private DutyAppInitializer dutyAppInitializer;
    @Mock
    private DutyPlanStatusTask dutyPlanStatusTask;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void run(){
        dutyPlanStatusTask.updateDutyPlanStatus();
        ApplicationArguments args = null;
        dutyAppInitializer.run(args);
        Mockito.verify(dutyPlanStatusTask,Mockito.times(1)).updateDutyPlanStatus();
    }

}