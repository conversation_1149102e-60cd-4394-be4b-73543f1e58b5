package com.zte.uedm.maintenance.common.a_infrastructure.cache.impl;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.component.kafka.producer.constants.KafkaActionOptional;
import com.zte.uedm.maintenance.common.a_infrastructure.cache.manager.GroupCacheManager;
import com.zte.uedm.maintenance.common.a_infrastructure.cache.strategy.GroupCacheManagerStrategy;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

public class GroupCacheManagerStrategyTest {

    /* Started by AICoder, pid:q77cdx539cfffcd147220b3e2013a62ddf24a4ce */
    @Mock
    private GroupCacheManager groupCacheManager;

    @InjectMocks
    private GroupCacheManagerStrategy groupCacheManagerStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Ended by AICoder, pid:q77cdx539cfffcd147220b3e2013a62ddf24a4ce */
    @Test
    public void dealKafka() throws UedmException {
        groupCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_CREATE, Collections.singleton("site"));
        groupCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_DELETE, Collections.singleton("site"));
        groupCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_UPDATE, Collections.singleton("site"));
    }

}