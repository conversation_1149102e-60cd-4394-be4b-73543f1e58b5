package com.zte.uedm.maintenance.common.a_domain.cache.provider;

import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.service.config.api.configuraiton.CollectorService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;


public class CollectorCacheDataProviderTest {

    @InjectMocks
    private CollectorCacheDataProvider cacheDataProvider;

    @Mock
    private CollectorService collectorService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:49d8bp2621c9e701448c0b34a091bb5f1601ca7a */
    @Test
    public void testGetCacheDataForCacheProvider_emptyKeys() throws UedmException {
        Mockito.when(collectorService.queryAll()).thenReturn(new ArrayList<>());
        Assert.assertTrue(cacheDataProvider.getCacheDataForCacheProvider(new HashSet()).isEmpty());
    }

    @Test
    public void testGetCacheDataForCacheProvider_nonEmptyKeys() throws UedmException {
        cacheDataProvider.toString();
        Mockito.when(collectorService.queryByIds(Mockito.any())).thenReturn(new ArrayList<>());
        Set<String> id = new HashSet<>();
        id.add("a");
        Assert.assertTrue(cacheDataProvider.getCacheDataForCacheProvider(id).isEmpty());
    }
    /* Ended by AICoder, pid:49d8bp2621c9e701448c0b34a091bb5f1601ca7a */
}
