package com.zte.uedm.maintenance.workbench.interfaces.web.dto;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class NoticeDTOTest {

    @Test
    public void noticeDTOTest() throws Exception {
        NoticeDTO noticeDTO = new NoticeDTO();
        PojoTestUtil.TestForPojo(noticeDTO.getClass());
        noticeDTO.setId("xxx");
        Assert.assertNotNull(noticeDTO.toString());
        Assert.assertEquals("xxx",  noticeDTO.getId());
    }
}
