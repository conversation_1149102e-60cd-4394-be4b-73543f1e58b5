package com.zte.uedm.maintenance.malfunction.application.executor.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.maintenance.malfunction.application.query.MalfunctionListQuery;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.repository.MalfunctionRepository;
import com.zte.uedm.maintenance.malfunction.domain.exporter.MalfunctionListDtoExporter;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionDetailDto;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionListDto;
import com.zte.uedm.maintenance.malfunction.interfaces.web.Dto.MalfunctionListOfMineDto;
import com.zte.uedm.maintenance.manager.infrastructure.client.user.UserRpc;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.UserNameUtils;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.InspectGroupUserBean;
import com.zte.uedm.maintenance.manager.interfaces.web.bean.UserBean;
import com.zte.uedm.maintenance.workflow.activiti.bean.HistoryOperationBean;
import com.zte.uedm.maintenance.workflow.activiti.enums.MalfunctionOrderStatusEnums;
import com.zte.uedm.maintenance.workflow.activiti.service.MalfunctionActivitiService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class MalfunctionQueryServiceImplTest {
    @InjectMocks
    private MalfunctionQueryServiceImpl malfunctionQueryService;

    @Mock
    private MalfunctionRepository malfunctionRepository;

    @Mock
    private MalfunctionActivitiService malfunctionActivitiService;

    @Mock
    private UserNameUtils userNameUtils;


    @Mock
    private UserRpc userRpc;

    @Mock
    private JsonService jsonService;
    @Mock
    private MalfunctionListDtoExporter malfunctionListDtoExporter;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @Mock
    private I18nUtils i18nUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
    }


    @Test
    public void checkNameTest(){
        Mockito.doReturn(true).when(malfunctionRepository).checkName(any(), any());
        ResponseBean responseBean= malfunctionQueryService.checkName("aaa","bbb");
        Assert.assertEquals(Integer.valueOf(0),responseBean.getCode());
    }

    @Test
    public void checkName2Test(){
        Mockito.doReturn(false).when(malfunctionRepository).checkName(any(), any());
        ResponseBean responseBean= malfunctionQueryService.checkName("aaa","bbb");
        Assert.assertEquals(Integer.valueOf(-1),responseBean.getCode());
    }

    @Test
    public void malfunctionClassifyTest(){

        List<Map<String, Object>> list = new ArrayList<>();
        Mockito.doReturn(list).when(malfunctionRepository).getMalfunctionClassify();
        List<Map<String, Object>> mapList=malfunctionQueryService.malfunctionClassify();
        Assert.assertEquals(0,mapList.size());
    }

    @Test
    public void queryListAllTest()
    {
        Mockito.doReturn(new Page<>()).when(malfunctionRepository).queryListHandled(any());
        MalfunctionListQuery query = new MalfunctionListQuery();
        malfunctionQueryService.queryListAll(query);
        malfunctionQueryService.queryListCreated(query);
        malfunctionQueryService.queryListPending(query);
        malfunctionQueryService.queryListProcessed(query);
        IPage<MalfunctionListDto> malfunctionListDtoIPage = malfunctionQueryService.queryListHandled(query);
        Assert.assertEquals(0,(int)malfunctionListDtoIPage.getTotal());
    }

    @Test
    public void queryListProcessedTest()
    {
        MalfunctionDetailDto malfunctionDetailDto = new MalfunctionDetailDto();
        malfunctionDetailDto.setId("1");
        malfunctionDetailDto.setWorkflowId("1");
        Mockito.doReturn(malfunctionDetailDto).when(malfunctionRepository).selectById(any());
        Mockito.doReturn(Collections.singletonList(new HistoryOperationBean())).when(malfunctionActivitiService).getTimelineByProcessInstanceId(any(), any());
        MalfunctionDetailDto malfunctionDetailDto1 = malfunctionQueryService.queryDetails("1");
        Assert.assertEquals("1",malfunctionDetailDto1.getId());
    }

    @Test
    public void hangupPreStatusTest() throws UedmException {
        Mockito.doReturn(MalfunctionOrderStatusEnums.PENDING).when(malfunctionActivitiService).getCurrentActivityStatus(any());
        ResponseBean result=malfunctionQueryService.hangupPreStatus("aaa");
        Assert.assertEquals(result.getCode(),Integer.valueOf(0));
    }

    @Test
    public void hangupPreStatusERRORTest() throws UedmException {
        Mockito.doThrow(new UedmException(-1,"aaa")).when(malfunctionActivitiService).getCurrentActivityStatus(any());
        try {
            ResponseBean result=malfunctionQueryService.hangupPreStatus("aaa");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-1));
        }
    }

    @Test
    public void getUsersByConditionTest() throws IOException, UedmException {
        ResponseBean responseBean = new ResponseBean();
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> value = mock(Call.class);
        when(userRpc.getAllUserByGroup(any(), any())).thenReturn(value);
        when(value.execute()).thenReturn(response);
        when(jsonService.jsonToObject(any(), any(), any())).thenReturn(new ArrayList<>());

        ResponseBean responseBean1 = malfunctionQueryService.getUsersByCondition(new InspectGroupUserBean());
        Assert.assertEquals(responseBean1.getCode(),Integer.valueOf(0));

        when(userRpc.getAllUserByGroup(any(), any())).thenThrow(new RuntimeException("aaa"));
        ResponseBean responseBean2 = malfunctionQueryService.getUsersByCondition(new InspectGroupUserBean());
        Assert.assertEquals(responseBean2.getCode(),Integer.valueOf(0));
    }

    @Test
    public void getFuzzyUsersExcludeAdminTest() throws IOException, UedmException {
        ResponseBean responseBean = new ResponseBean();
        Response<ResponseBean> response = Response.success(responseBean);
        Call<ResponseBean> value = mock(Call.class);
        when(userRpc.getAllUserByGroup(any(), any())).thenReturn(value);
        when(value.execute()).thenReturn(response);
        List<UserBean> retList = new ArrayList<>();
        UserBean userBean = new UserBean();
        userBean.setUserName("aaa");
        retList.add(userBean);
        UserBean userBean2 = new UserBean();
        userBean.setUserName("admin");
        retList.add(userBean2);
        when(jsonService.jsonToObject(any(), any(), any())).thenReturn(retList);
        InspectGroupUserBean searchCondition = new InspectGroupUserBean();
        searchCondition.setPageSize(1);
        searchCondition.setPageNo(1);
        searchCondition.setUserGroupName("aaa");
        searchCondition.setUserGroupName("");
        ResponseBean responseBean1 = malfunctionQueryService.getFuzzyUsersExcludeAdmin(searchCondition);
        Assert.assertEquals(responseBean1.getCode(),Integer.valueOf(0));
    }

    @Test
    public void getAllUserByGroupTest1(){
        boolean flag1 = false;
        try {
            ResponseBean responseBean = new ResponseBean();
            Response<ResponseBean> response = Response.success(responseBean);
            Call<ResponseBean> value = mock(Call.class);
            when(userRpc.getAllUserByGroup(any(), any())).thenReturn(value);
            when(value.execute()).thenReturn(response);

            //反射 改状态码为400 模拟失败分支
            Field rawResponse = response.getClass().getDeclaredField("rawResponse");
            rawResponse.setAccessible(true);
            okhttp3.Response response1 = (okhttp3.Response)rawResponse.get(response);
            Field code = response1.getClass().getDeclaredField("code");
            code.setAccessible(true);
            code.set(response1,500);
            malfunctionQueryService.getUsersByCondition(new InspectGroupUserBean());
        } catch (Exception e) {
            flag1 = true;
        }

        Assert.assertFalse(flag1);
    }

    @Test
    public void existTaskTest() throws UedmException {
        Boolean flag = true;
        try {
            malfunctionQueryService.existTask("1");
        } catch (UedmException e) {
            flag = false;
        }
        Assert.assertFalse(flag);
    }

    @Test
    public void existTaskTest1() throws UedmException {
        Boolean flag = true;
        try {
            Mockito.doReturn(true).when(malfunctionRepository).findByRelateOrderId(any(), any());
            malfunctionQueryService.existTask("1");
        } catch (UedmException e) {
            flag = false;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void queryMalfunctionListOfMineTest()
    {
        Mockito.doReturn(new MalfunctionListOfMineDto()).when(malfunctionRepository).queryListOfMine(any(), any());
        MalfunctionListQuery query = new MalfunctionListQuery();
        String userName = "admin";
        Assert.assertEquals(null, malfunctionQueryService.queryMalfunctionListOfMine(query, userName).getMalfunctionListOfMine());
    }

    @Test
    public void testExport() throws UedmException {
        List<MalfunctionListDto> records = new ArrayList<>();
        String languageOption = "English";
        String exportType = "Initiated";
        MalfunctionListDto malfunctionListDto = new MalfunctionListDto();
        malfunctionListDto.setStatus(0);
        malfunctionListDto.setMalfunctionSource(0);
        malfunctionListDto.setEffectLevel(0);
        malfunctionListDto.setEmergencyLevel(0);
        malfunctionListDto.setMalfunctionLevel(0);
        records.add(malfunctionListDto);
        when(malfunctionListDtoExporter.export(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn("MockedExportResult");

        String result = malfunctionQueryService.export(records, request, response, languageOption, exportType);

        assertNotNull(result);
    }
}
