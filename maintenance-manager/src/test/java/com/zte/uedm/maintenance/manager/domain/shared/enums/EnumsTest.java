package com.zte.uedm.maintenance.manager.domain.shared.enums;

import org.junit.Assert;
import org.junit.Test;

public class EnumsTest {
    @Test
    public void maintenancePlanStatusEnumsTest(){
        MaintenancePlanStatusEnums[] values = MaintenancePlanStatusEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getStatus();
            String name1 = values[i - 1].getName();
            MaintenancePlanStatusEnums inspectPlanStatusEnums = MaintenancePlanStatusEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(name1,inspectPlanStatusEnums.getName());
            Assert.assertEquals(status,inspectPlanStatusEnums.getStatus());
        }
        String nameByStatus = MaintenancePlanStatusEnums.getNameByStatus(1);
        Assert.assertEquals(MaintenancePlanStatusEnums.NOT_ENABLED.getName(),nameByStatus);
    }

    @Test
    public void maintenanceReminderModeEnumsTest(){
        MaintenanceReminderModeEnums[] values = MaintenanceReminderModeEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getType();
            String name1 = values[i - 1].getName();
            MaintenanceReminderModeEnums inspectPlanStatusEnums = MaintenanceReminderModeEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(name1,inspectPlanStatusEnums.getName());
            Assert.assertEquals(status,inspectPlanStatusEnums.getType());
        }
        String nameByType = MaintenanceReminderModeEnums.getNameByType(1);
        Assert.assertEquals(MaintenanceReminderModeEnums.NONE.getName(),nameByType);
    }

    @Test
    public void taskRoleGrainEnumsTest(){
        TaskRoleGrainEnums[] values = TaskRoleGrainEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            String status = values[i - 1].getGrainName();
            String statusZH = values[i - 1].getGrainNameZH();
            TaskRoleGrainEnums inspectPlanStatusEnums = TaskRoleGrainEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(statusZH,inspectPlanStatusEnums.getGrainNameZH());
            Assert.assertEquals(status,inspectPlanStatusEnums.getGrainName());
        }

        TaskRoleGrainEnums day = TaskRoleGrainEnums.getGrainByName("day");
        Assert.assertEquals(TaskRoleGrainEnums.DAY,day);
    }

    @Test
    public void maintenanceTaskConclusionEnumsTest(){
        MaintenanceTaskConclusionEnums[] values = MaintenanceTaskConclusionEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getType();
            MaintenanceTaskConclusionEnums inspectPlanStatusEnums = MaintenanceTaskConclusionEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(status,inspectPlanStatusEnums.getType());
        }
    }

    @Test
    public void maintenanceTaskStatusEnumsTest(){
        MaintenanceTaskStatusEnums[] values = MaintenanceTaskStatusEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getStatus();
            String name1 = values[i - 1].getName();
            MaintenanceTaskStatusEnums inspectPlanStatusEnums = MaintenanceTaskStatusEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(name1,inspectPlanStatusEnums.getName());
            Assert.assertEquals(status,inspectPlanStatusEnums.getStatus());
        }

        MaintenanceTaskStatusEnums enumByStatus = MaintenanceTaskStatusEnums.getEnumByStatus(1);
        Assert.assertEquals(enumByStatus,MaintenanceTaskStatusEnums.WAIT_EXECUTE);
    }

    @Test
    public void planOperationErrorEnumsTest(){
        PlanOperationErrorEnums[] values = PlanOperationErrorEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getCode();
            String msg = values[i - 1].getMsg();
            PlanOperationErrorEnums inspectPlanStatusEnums = PlanOperationErrorEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(msg,inspectPlanStatusEnums.getMsg());
            Assert.assertEquals(status,inspectPlanStatusEnums.getCode());
        }
    }

    @Test
    public void maintenanceTaskLogConclusionEnumsTest(){
        MaintenanceTaskLogConclusionEnums[] values = MaintenanceTaskLogConclusionEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            MaintenanceTaskLogConclusionEnums maintenanceTaskLogConclusionEnums = MaintenanceTaskLogConclusionEnums.valueOf(name);
            Assert.assertEquals(name,maintenanceTaskLogConclusionEnums.name());
        }
        String cnString=MaintenanceTaskLogConclusionEnums.getCnByCode(1);
        Assert.assertEquals(cnString,"正常");
        String enString=MaintenanceTaskLogConclusionEnums.getEnByCode(1);
        Assert.assertEquals(enString,"normal");
        String cnString2=MaintenanceTaskLogConclusionEnums.getCnByCode(6);
        Assert.assertNull(cnString2);
        String enString2=MaintenanceTaskLogConclusionEnums.getEnByCode(6);
        Assert.assertNull(enString2);
    }

    @Test
    public void maintenanceTaskLogStatusEnumsTest(){
        MaintenanceTaskLogStatusEnums[] values = MaintenanceTaskLogStatusEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            MaintenanceTaskLogStatusEnums maintenanceTaskLogStatusEnums = MaintenanceTaskLogStatusEnums.valueOf(name);
            Assert.assertEquals(name,maintenanceTaskLogStatusEnums.name());
        }
        String cnString=MaintenanceTaskLogStatusEnums.getCnByCode(1);
        Assert.assertEquals(cnString,"待执行");
        String enString=MaintenanceTaskLogStatusEnums.getEnByCode(1);
        Assert.assertEquals(enString,"wait execute");
        String cnString2=MaintenanceTaskLogStatusEnums.getCnByCode(8);
        Assert.assertNull(cnString2);
        String enString2=MaintenanceTaskLogStatusEnums.getEnByCode(8);
        Assert.assertNull(enString2);
    }

    @Test
    public void maintenancePlanTypeEnumsTest(){
        MaintenancePlanTypeEnums[] values = MaintenancePlanTypeEnums.values();
        for (int i = 1; i <= values.length; i++) {
            String name = values[i - 1].name();
            Integer status = values[i - 1].getType();
            String msg = values[i - 1].getName();
            MaintenancePlanTypeEnums inspectPlanStatusEnums = MaintenancePlanTypeEnums.valueOf(name);
            Assert.assertEquals(name,inspectPlanStatusEnums.name());
            Assert.assertEquals(msg,inspectPlanStatusEnums.getName());
            Assert.assertEquals(status,inspectPlanStatusEnums.getType());
        }

        String nameByType = MaintenancePlanTypeEnums.getNameByType(1);
        Assert.assertEquals(MaintenancePlanTypeEnums.NOT_ENABLED.getName(),nameByType);
    }

}
