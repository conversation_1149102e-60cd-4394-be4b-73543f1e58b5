package com.zte.uedm.maintenance.common.a_domain.aggregate.standpoint.model.obj;

import com.zte.uedm.maintenance.common.a_domain.aggregate.standardpoint.model.obj.PointTypeOptional;
import org.junit.Test;

import static org.junit.Assert.assertTrue;

public class PointTypeOptionalTest {
    
    /* Started by AICoder, pid:odc61jd82f7e5131465b0bdc80a0ce09747978dd */
    @Test
    public void pointTypeOptionalTest() throws Exception {
        PointTypeOptional.getById("AI");
        PointTypeOptional.getById("xx");
        assertTrue(PointTypeOptional.POINT_TYPE_AI.getId().equals("AI"));
        assertTrue(PointTypeOptional.POINT_TYPE_DI.getId().equals("DI"));
        assertTrue(PointTypeOptional.POINT_TYPE_AO.getName().equals("{\"en_US\":\"Parameter\"," + "\"zh_CN\":\"参数\"}"));
        assertTrue(PointTypeOptional.POINT_TYPE_DO.getId().equals("DO"));
    }
    /* Ended by AICoder, pid:odc61jd82f7e5131465b0bdc80a0ce09747978dd */
}