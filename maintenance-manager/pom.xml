<?xml version="1.0" encoding="UTF-8" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zte.uedm</groupId>
        <artifactId>uedm-maintenance</artifactId>
        <version>${revision}</version>
    </parent>



    <modelVersion>4.0.0</modelVersion>
    <artifactId>uedm-maintenance-manager</artifactId>
    <name>维保管理</name>

    <properties>
        <app.mainclass>com.zte.uedm.maintenance.BootstrapApp</app.mainclass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-log-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>data-transfer-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.quartz-scheduler</groupId>
                    <artifactId>quartz</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-util-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.zte.uedm</groupId>-->
        <!--            <artifactId>msb-init-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-backup-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-basis-starter-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-mp-starter-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-pma-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-redis-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-security-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-thread-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-msb-starter-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-db-jdbc</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-kafka</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.11</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.quartz-scheduler</groupId>
                    <artifactId>quartz</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-kafka-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-redis-redisson</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.projectreactor</groupId>
                    <artifactId>reactor-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>redis-service-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.zte.uedm</groupId>-->
        <!--            <artifactId>kafka-consumer-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.zte.uedm</groupId>-->
        <!--            <artifactId>kafka-producer-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-configcenter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-i18n</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-rpc-retrofit</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-websocket</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-uiframe</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-configuration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-full</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-lite</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.vandeseer</groupId>
            <artifactId>easytable</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>au.com.dius</groupId>-->
        <!--            <artifactId>pact-jvm-provider-spring_2.12</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.jetbrains.kotlin</groupId>-->
        <!--                    <artifactId>kotlin-stdlib</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>imop-cma-log-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-orm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-bpmn-layout</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>log-trace-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.bundles.repackaged</groupId>
            <artifactId>jersey-guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-config-starter-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm</groupId>
            <artifactId>common-caffeine-starter-autoconfigure</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.8</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>


                <configuration>
                    <rules>
                        <rule implementation="org.jacoco.maven.RuleConfiguration">
                            <element>BUNDLE</element>
                            <limits>
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>INSTRUCTION</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.00</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <!-- <executions> <execution> <id>attach-sources</id> <goals> <goal>jar</goal>
                    </goals> </execution> </executions> -->
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeScope>runtime</includeScope>
                    <outputDirectory>target/lib</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>*</include>
                                        <include>dbscript/**</include>
                                        <include>menus/uedm-maintenance-menus.json</include>
                                    </includes>
                                </resource>
                            </resources>
                            <overwrite>true</overwrite>
                            <nonFilteredFileExtensions>
                                <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                            </nonFilteredFileExtensions>
                        </configuration>
                    </execution>

                    <execution>
                        <id>copy-resources-script</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/script</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/script</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>*</include>
                                    </includes>
                                </resource>
                            </resources>
                            <overwrite>true</overwrite>
                        </configuration>
                    </execution>

                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Class-Path>.</Class-Path>
                        </manifestEntries>
                    </archive>
                    <excludes>
                        <exclude>application.yml</exclude>
                        <exclude>application-prod.yml</exclude>
                        <exclude>banner.txt</exclude>
                        <exclude>logback.xml</exclude>
                        <exclude>/menus/</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>distribution</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target name="distribution">
                                <tar
                                        destfile="target/${project.artifactId}-${project.version}.tar.gz"
                                        longfile="posix" compression="gzip">
                                    <tarfileset dir="target" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes">
                                        <include name="${project.artifactId}-${project.version}.jar"/>
                                    </tarfileset>
                                    <tarfileset dir="target/script" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes">
                                        <include name="*"/>
                                    </tarfileset>
                                    <tarfileset dir="target" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes">
                                        <include name="conf/**"/>
                                    </tarfileset>
                                    <tarfileset dir="target" filemode="0540" dirmode="0750" uid="3001" gid="3001" username="oes" group="oes">
                                        <include name="lib/**"/>
                                    </tarfileset>
                                </tar>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>target/${project.artifactId}-${project.version}.tar.gz</file>
                                    <type>tar.gz</type>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <testFailureIgnore>true</testFailureIgnore>
                    <forkMode>once</forkMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.kongchen</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>3.1.8</version>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <springmvc>false</springmvc>
                            <locations>
                                <location>com.zte.uedm.maintenance</location>
                            </locations>
                            <basePath>/api/maintenance-manager/v1</basePath>
                            <info>
                                <title>maintenance-manager</title>
                                <version>v1</version>
                                <description>maintenance-manager-API</description>
                            </info>
                            <outputFormats>json</outputFormats>
                            <swaggerDirectory>swagger/maintenance-manager</swaggerDirectory>
                            <swaggerApiReader>com.github.kongchen.swagger.docgen.reader.SwaggerReader</swaggerApiReader>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.kongchen</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>3.1.8</version>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <springmvc>false</springmvc>
                            <locations>
                                <location>com.zte.uedm.maintenance.drill.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.duty.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.malfunction.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.manager.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.order.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.risk.interfaces.web</location>
                                <location>com.zte.uedm.maintenance.workbench.interfaces.web</location>
                            </locations>
                            <basePath>/api/maintenance-manager/v1</basePath>
                            <info>
                                <title>maintenance-manager</title>
                                <version>v1</version>
                                <description>maintenance-manager-API</description>
                            </info>
                            <outputFormats>json</outputFormats>
                            <swaggerDirectory>swagger/maintenance-manager</swaggerDirectory>
                            <swaggerApiReader>com.github.kongchen.swagger.docgen.reader.SwaggerReader</swaggerApiReader>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-maven-plugin</artifactId>
                <version>4.34.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <schemaVersion>DRAFT_2020_12</schemaVersion>
                    <classNames>
                        <className>com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.ActiveAlarmBody</className>
                        <className>com.zte.uedm.maintenance.drill.interfaces.web.bean.topo.TopoDetailQueryBean</className>
                        <className>com.zte.uedm.maintenance.drill.interfaces.web.beans.topo.TopoListRequestBean</className>
                        <className>com.zte.uedm.maintenance.drill.interfaces.web.beans.topo.TopoTreeRequestBean</className>
                        <className>com.zte.uedm.maintenance.duty.interfaces.web.bean.UserMessageBean</className>
                        <className>com.zte.uedm.maintenance.duty.interfaces.web.vo.InspectGroupUserQueryVO</className>
                        <className>com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.HistoryAlarmBody</className>
                        <className>com.zte.uedm.maintenance.malfunction.infrastructure.client.license.bean.LicenseQueryDTO</className>
                    </classNames>
                    <schemaFilePath>target/pbc/publishedEvents/UEDM_MAINTENANCE_MAINTENANCE/</schemaFilePath>
                    <schemaFileName>{0}.schema.json</schemaFileName>
                    <modules>
                        <module>
                            <name>Swagger2</name>
                        </module>
                    </modules>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
